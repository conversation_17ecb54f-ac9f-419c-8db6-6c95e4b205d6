-- 气密性测试数据插入脚本

-- 插入气密性测试数据
INSERT INTO airtightness_tests (
    vehicle_model_id, test_date, test_engineer, test_location,
    uncontrolled_leakage, left_pressure_valve, right_pressure_valve, 
    ac_circulation_valve, right_door_drain_hole, tailgate_drain_hole,
    right_door_outer_seal, right_door_outer_opening, side_mirrors, 
    body_shell_leakage, other_area, notes, updated_by
) VALUES 
-- 宝骏530 (vehicle_model_id = 1)
(1, '2023-12-01', '张工', '上海实验室', 
 2.5, 0.8, 0.9, 1.2, 0.5, 0.6, 0.3, 0.4, 0.7, 1.8, 0.2, 
 '宝骏530气密性测试，整体表现良好', 'admin'),

-- 宝骏510 (vehicle_model_id = 2)
(2, '2023-12-02', '李工', '上海实验室', 
 2.8, 0.9, 1.0, 1.3, 0.6, 0.7, 0.4, 0.5, 0.8, 2.0, 0.3, 
 '宝骏510气密性测试，部分区域需要优化', 'admin'),

-- 五菱宏光MINI EV (vehicle_model_id = 3)
(3, '2023-12-03', '王工', '上海实验室', 
 1.9, 0.6, 0.7, 0.9, 0.4, 0.5, 0.2, 0.3, 0.5, 1.5, 0.1, 
 '五菱宏光MINI EV气密性测试，表现优秀', 'admin');

-- 插入气密性测试图片数据
INSERT INTO airtightness_images (
    vehicle_model_id, front_compartment_image, door_image, tailgate_image, 
    upload_date, notes
) VALUES
-- 宝骏530图片
(1, 'airtightness_images/front_compartment/bmw530_front.jpg', 
    'airtightness_images/doors/bmw530_door.jpg', 
    'airtightness_images/tailgate/bmw530_tailgate.jpg', 
    '2023-12-01', '宝骏530气密性测试图片'),

-- 宝骏510图片
(2, 'airtightness_images/front_compartment/bmw510_front.jpg', 
    'airtightness_images/doors/bmw510_door.jpg', 
    'airtightness_images/tailgate/bmw510_tailgate.jpg', 
    '2023-12-02', '宝骏510气密性测试图片'),

-- 五菱宏光MINI EV图片
(3, 'airtightness_images/front_compartment/wuling_front.jpg', 
    'airtightness_images/doors/wuling_door.jpg', 
    'airtightness_images/tailgate/wuling_tailgate.jpg', 
    '2023-12-03', '五菱宏光MINI EV气密性测试图片');

-- 查询验证数据
SELECT 
    v.vehicle_model_name,
    at.test_date,
    at.test_engineer,
    at.uncontrolled_leakage,
    at.left_pressure_valve,
    at.right_pressure_valve,
    at.ac_circulation_valve
FROM airtightness_tests at
JOIN vehicle_models v ON at.vehicle_model_id = v.id
ORDER BY at.test_date;
