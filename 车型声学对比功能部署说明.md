# 车型声学对比功能部署说明

## 概述

本文档说明新增的两个车型声学对比功能的实现和部署方法：
1. **车型隔声量对比功能**
2. **车型混响时间对比功能**

这两个功能已成功集成到现有的吸隔声模块中，采用与"区域隔声量"功能相同的架构和用户体验。

## 功能特性

### 1. 车型隔声量对比功能
- **功能描述**: 对比不同车型的隔声量数据
- **数据范围**: 18个中心频率点（200Hz-10000Hz）
- **数据单位**: dB（分贝）
- **精度**: 小数点后2位
- **对比方式**: 多车型同时对比，生成表格和图表

### 2. 车型混响时间对比功能
- **功能描述**: 对比不同车型的混响时间数据
- **数据范围**: 18个中心频率点（200Hz-10000Hz）
- **数据单位**: s（秒）
- **精度**: 小数点后3位
- **对比方式**: 多车型同时对比，生成表格和图表

### 3. 共同特性
- **多车型选择**: 支持同时选择多个车型进行对比
- **交互式图表**: 使用ECharts生成可交互的折线图
- **测试图片查看**: 点击图表线条或按钮查看测试附图
- **数据导出**: 支持CSV格式数据导出
- **响应式设计**: 适配不同屏幕尺寸
- **一致的UI风格**: 与现有功能保持界面一致性

## 文件结构

### 后端文件
```
models/
├── vehicle_sound_insulation_model.py    # 车型隔声量数据模型
├── vehicle_reverberation_model.py       # 车型混响时间数据模型
└── __init__.py                          # 已更新，添加新模型导入

services/
└── sound_insulation_service.py         # 已扩展，添加新功能业务逻辑

controllers/
└── sound_insulation_controller.py      # 已扩展，添加新功能API接口
```

### 前端文件
```
templates/sound_insulation/
├── area_comparison.html                 # 现有区域隔声量对比页面
├── vehicle_insulation.html             # 新增：车型隔声量对比页面
└── vehicle_reverberation.html          # 新增：车型混响时间对比页面

static/js/
├── sound_insulation.js                 # 现有基础功能
├── vehicle_insulation.js               # 新增：车型隔声量功能
└── vehicle_reverberation.js            # 新增：车型混响时间功能

static/css/
└── sound_insulation.css                # 复用现有样式
```

### 配置文件
```
templates/base.html                      # 已更新，添加新的导航菜单项
create_vehicle_sound_tables.sql         # 数据库表创建和测试数据脚本
init_vehicle_sound_db.py                # 数据库初始化脚本
test_vehicle_sound_features.py          # 功能测试脚本
```

## 数据库结构

### 1. 车型隔声量数据表 (vehicle_sound_insulation_data)
```sql
CREATE TABLE vehicle_sound_insulation_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_model_id INT NOT NULL COMMENT '车型ID',
    
    -- 18个频率字段 (200Hz-10000Hz)
    freq_200 DECIMAL(5,2) COMMENT '200Hz隔声量(dB)',
    freq_250 DECIMAL(5,2) COMMENT '250Hz隔声量(dB)',
    -- ... 其他频率字段
    freq_10000 DECIMAL(5,2) COMMENT '10000Hz隔声量(dB)',
    
    -- 测试信息字段
    test_image_path VARCHAR(500) COMMENT '测试图片路径',
    test_date DATE COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) COMMENT '测试工程师',
    remarks TEXT COMMENT '备注',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (vehicle_model_id) REFERENCES vehicle_models(id),
    UNIQUE KEY unique_vehicle_insulation (vehicle_model_id)
);
```

### 2. 车型混响时间数据表 (vehicle_reverberation_data)
```sql
CREATE TABLE vehicle_reverberation_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_model_id INT NOT NULL COMMENT '车型ID',
    
    -- 15个频率字段 (400Hz-10000Hz)
    freq_400 DECIMAL(5,3) COMMENT '400Hz混响时间(s)',
    freq_500 DECIMAL(5,3) COMMENT '500Hz混响时间(s)',
    -- ... 其他频率字段
    freq_10000 DECIMAL(5,3) COMMENT '10000Hz混响时间(s)',
    
    -- 测试信息字段（与隔声量表结构相同）
    test_image_path VARCHAR(500) COMMENT '测试图片路径',
    test_date DATE COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) COMMENT '测试工程师',
    remarks TEXT COMMENT '备注',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (vehicle_model_id) REFERENCES vehicle_models(id),
    UNIQUE KEY unique_vehicle_reverberation (vehicle_model_id)
);
```

## 部署步骤

### 1. 数据库初始化
```bash
# 方法1: 使用Python脚本初始化
python init_vehicle_sound_db.py

# 方法2: 手动执行SQL脚本
# 在MySQL中执行 create_vehicle_sound_tables.sql 文件
```

### 2. 测试数据导入
执行 `create_vehicle_sound_tables.sql` 文件中的INSERT语句，导入测试数据：
- 3个车型的隔声量数据（宝骏530、宝骏510、五菱宏光MINI EV）
- 3个车型的混响时间数据
- 每个车型包含15个频率点的完整数据（400Hz-10000Hz）

### 3. 功能测试
```bash
# 运行功能测试脚本
python test_vehicle_sound_features.py
```

### 4. 应用启动
```bash
# 启动应用
python run.py
```

## API接口

### 车型隔声量对比API

#### 1. 获取车型列表
```
GET /sound_insulation/api/vehicle_insulation/vehicles
```

#### 2. 生成对比数据
```
POST /sound_insulation/api/vehicle_insulation/comparison
Body: {
    "vehicle_ids": [1, 2, 3]
}
```

#### 3. 获取测试图片
```
GET /sound_insulation/api/vehicle_insulation/test_image?vehicle_id={vehicle_id}
```

#### 4. 导出数据
```
POST /sound_insulation/api/vehicle_insulation/export
Body: {
    "vehicle_ids": [1, 2]
}
```

### 车型混响时间对比API

#### 1. 获取车型列表
```
GET /sound_insulation/api/vehicle_reverberation/vehicles
```

#### 2. 生成对比数据
```
POST /sound_insulation/api/vehicle_reverberation/comparison
Body: {
    "vehicle_ids": [1, 2, 3]
}
```

#### 3. 获取测试图片
```
GET /sound_insulation/api/vehicle_reverberation/test_image?vehicle_id={vehicle_id}
```

#### 4. 导出数据
```
POST /sound_insulation/api/vehicle_reverberation/export
Body: {
    "vehicle_ids": [1, 2]
}
```

## 访问入口

### 1. 导航菜单
- **路径**: 左侧菜单 → 吸隔声模块 → 车型隔声量对比
- **路径**: 左侧菜单 → 吸隔声模块 → 车型混响时间对比

### 2. 直接访问
- **车型隔声量对比**: http://localhost:5000/sound_insulation/vehicle_insulation
- **车型混响时间对比**: http://localhost:5000/sound_insulation/vehicle_reverberation

## 使用说明

### 1. 操作流程
1. **选择车型**: 从多选框中选择一个或多个车型
2. **生成对比**: 点击"生成对比表"按钮
3. **查看结果**: 
   - 查看数据表格（频率为行，车型为列）
   - 查看折线对比图
   - 查看测试信息表
4. **查看附图**: 点击图表线条或"查看附图"按钮
5. **导出数据**: 点击"导出数据"按钮下载CSV文件

### 2. 界面说明
- **查询条件区域**: 车型多选框和生成按钮
- **数据表格**: 频率固定列 + 车型数据列，支持横向滚动
- **折线图**: 交互式图表，支持悬停和点击
- **测试信息表**: 显示各车型的测试详情
- **图片模态框**: 显示测试附图和详细信息

## 技术特点

### 1. 架构设计
- **MVC架构**: 模型-视图-控制器分离
- **服务层**: 业务逻辑封装
- **代码复用**: 继承现有功能架构
- **模块化**: 功能独立，易于维护

### 2. 前端技术
- **Bootstrap 5**: 响应式UI框架
- **ECharts 5**: 数据可视化图表库
- **原生JavaScript**: 交互逻辑实现
- **CSS3**: 复用现有样式

### 3. 后端技术
- **Flask**: Web框架
- **SQLAlchemy**: ORM数据库操作
- **MySQL**: 关系型数据库
- **Python**: 服务端语言

## 扩展建议

### 1. 功能扩展
- 添加更多车型数据
- 支持频率范围自定义
- 增加统计分析功能
- 支持批量数据导入

### 2. 性能优化
- 数据缓存机制
- 图表渲染优化
- 大数据量分页处理

### 3. 用户体验
- 数据筛选功能
- 图表样式自定义
- 移动端适配优化

## 故障排除

### 1. 常见问题
- **数据不显示**: 检查数据库连接和数据完整性
- **图表不渲染**: 检查ECharts库加载和数据格式
- **导出失败**: 检查服务器权限和磁盘空间

### 2. 调试方法
- 使用 `test_vehicle_sound_features.py` 进行功能测试
- 检查浏览器控制台错误信息
- 查看Flask应用日志

## 总结

车型声学对比功能已成功实现并集成到现有系统中，提供了完整的车型隔声量和混响时间对比分析功能。新功能采用现代化的技术架构，具有良好的可扩展性和用户体验，与现有功能保持高度一致性。
