-- 创建数据库
CREATE DATABASE IF NOT EXISTS nvh_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE nvh_data;

-- 车型信息表
CREATE TABLE vehicle_models (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_model_code VARCHAR(50) UNIQUE NOT NULL COMMENT '车型代码',
    vehicle_model_name VARCHAR(100) NOT NULL COMMENT '车型名称',
    vin VARCHAR(50) UNIQUE NOT NULL COMMENT 'VIN码',
    drive_type VARCHAR(30) COMMENT '驱动类型',
    configuration VARCHAR(200) COMMENT '具体配置',
    production_year INT COMMENT '生产年份',
    STATUS ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '车型信息表';

-- 零部件表
CREATE TABLE components (
    id INT PRIMARY KEY AUTO_INCREMENT,
    component_code VARCHAR(50) UNIQUE NOT NULL COMMENT '零件代码',
    component_name VARCHAR(100) NOT NULL COMMENT '零件名称',
    category VARCHAR(100) NOT NULL COMMENT '主分类',
    sub_category VARCHAR(50) NOT NULL COMMENT '子分类',
    parent_id INT COMMENT '父级零件ID',
    DESCRIPTION TEXT COMMENT '描述',
    material VARCHAR(100) COMMENT '材料',
    weight DECIMAL(8,3) COMMENT '重量(kg)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES components(id)
) COMMENT '零部件表';

-- 测试项目表
CREATE TABLE test_projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_code VARCHAR(50) UNIQUE NOT NULL COMMENT '项目代码',
    project_name VARCHAR(100) NOT NULL COMMENT '项目名称',
    vehicle_model_id INT NOT NULL COMMENT '车辆ID',
    component_id INT COMMENT '零件ID',
    test_type VARCHAR(200) NOT NULL COMMENT '测试类型',
    test_date DATE NOT NULL COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) NOT NULL COMMENT '测试工程师',
    test_condition VARCHAR(200) COMMENT '测试条件',
    test_status VARCHAR(200) COMMENT '测试状态',
    excitation_method VARCHAR(100) COMMENT '激励方式',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_model_id) REFERENCES vehicle_models(id),
    FOREIGN KEY (component_id) REFERENCES components(id)
) COMMENT '测试项目表';

-- 模态数据表
CREATE TABLE modal_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    test_project_id INT NOT NULL COMMENT '测试项目ID',
    mode_order INT NOT NULL COMMENT '模态阶次',
    direction VARCHAR(100) COMMENT '方向',
    frequency DECIMAL(10,2) NOT NULL COMMENT '频率(Hz)',
    damping_ratio DECIMAL(6,2) COMMENT '阻尼比',
    mode_shape_description TEXT COMMENT '模态振型描述',
    mode_shape_file VARCHAR(255) COMMENT 'GIF动图文件路径',
    test_photo_file VARCHAR(255) COMMENT '测试照片文件路径',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(50) COMMENT '修改人员',
    FOREIGN KEY (test_project_id) REFERENCES test_projects(id)
) COMMENT '模态数据表';

-- 插入示例数据
INSERT INTO vehicle_models (vehicle_model_code, vehicle_model_name, vin, drive_type, configuration, production_year) VALUES
('SGM001', '宝骏530', 'LSGJA52U0JG123456', 'FWD', '1.5T CVT豪华型', 2023),
('SGM002', '宝骏510', 'LSGJA52U0JG123457', 'FWD', '1.5L MT舒适型', 2023),
('SGM003', '五菱宏光MINI EV', 'LSGJA52U0JG123458', 'RWD', '磷酸铁锂版', 2023);

INSERT INTO components (component_code, component_name, category, sub_category) VALUES
('SUSP001', '前悬架总成', '底盘系统', '悬架'),
('SUSP002', '后悬架总成', '底盘系统', '悬架'),
('ENG001', '发动机总成', '底盘系统', '动力总成'),
('BODY001', '车身骨架', '车身系统', '车身'),
('DOOR001', '前车门', '车身系统', '开闭件');

INSERT INTO test_projects (project_code, project_name, vehicle_model_id, component_id, test_type, test_date, test_engineer, test_condition, test_status, excitation_method) VALUES
('TEST001', '宝骏530整车模态测试', 1, NULL, '模态测试', '2023-12-01', '张工', '整车状态', '整车状态', '锤击法'),
('TEST002', '宝骏530前悬架模态测试', 1, 1, '模态测试', '2023-12-02', '李工', '自由状态', '零件自由状态', '激振器');

INSERT INTO modal_data (test_project_id, mode_order, direction, frequency, damping_ratio, mode_shape_description, notes, updated_by) VALUES
(1, 1, 'Z', 12.5, 0.05, '车身垂直弯曲', '第一阶整车模态', 'admin'),
(1, 2, 'Y', 15.8, 0.04, '车身侧向弯曲', '第二阶整车模态', 'admin'),
(2, 1, 'Z', 25.3, 0.08, '悬架垂直振动', '悬架第一阶模态', 'admin');
