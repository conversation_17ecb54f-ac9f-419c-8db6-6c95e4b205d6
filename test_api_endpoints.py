#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API接口
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_api_endpoints():
    """测试API接口"""
    print("🚀 开始测试API接口")
    
    try:
        # 测试零件列表API
        print("\n=== 测试零件列表API ===")
        response = requests.get(f"{BASE_URL}/sound_absorption/api/parts")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 测试材料列表API
        print("\n=== 测试材料列表API ===")
        response = requests.get(f"{BASE_URL}/sound_absorption/api/materials?part_name=前围隔音垫")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 测试克重列表API
        print("\n=== 测试克重列表API ===")
        response = requests.get(f"{BASE_URL}/sound_absorption/api/weights?part_name=前围隔音垫&material_name=PET纤维毡")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 测试吸音系数数据API
        print("\n=== 测试吸音系数数据API ===")
        response = requests.get(f"{BASE_URL}/sound_absorption/api/absorption_data?part_name=前围隔音垫&material_name=PET纤维毡&weight=800")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"成功获取数据，基础信息: {data['data']['basic_info']['part_name']} - {data['data']['basic_info']['material_name']}")
            print(f"表格数据行数: {len(data['data']['table_data'])}")
            print(f"图表频率点数: {len(data['data']['chart_data']['frequencies'])}")
        
        # 测试多克重对比API
        print("\n=== 测试多克重对比API ===")
        payload = {
            "part_name": "前围隔音垫",
            "material_name": "PET纤维毡",
            "weights": [800, 1200, 1600]
        }
        response = requests.post(
            f"{BASE_URL}/sound_absorption/api/multi_weight_comparison",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"成功获取对比数据，对比克重数: {len(payload['weights'])}")
            print(f"图表系列数: {len(data['data']['chart_data']['series'])}")
        
        # 测试测试图片信息API
        print("\n=== 测试测试图片信息API ===")
        response = requests.get(f"{BASE_URL}/sound_absorption/api/test_image?part_name=前围隔音垫&material_name=PET纤维毡&weight=800")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        print("\n🎉 API接口测试完成！")
        
    except Exception as e:
        print(f"✗ API测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_api_endpoints()
