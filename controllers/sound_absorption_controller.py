import csv
import io
from flask import Blueprint, request, render_template, make_response, send_file
from services.sound_absorption_service import SoundAbsorptionService
from utils.result import success, error, bad_request
from decorators import login_required

sound_absorption_bp = Blueprint('sound_absorption', __name__, url_prefix='/sound_absorption')
sound_absorption_service = SoundAbsorptionService()

class SoundAbsorptionController:
    """吸音系数控制器"""
    
    @staticmethod
    @sound_absorption_bp.route('/coefficient_query')
    @login_required
    def coefficient_query_page():
        """垂直入射法吸音系数查询页面"""
        return render_template('sound_absorption/coefficient_query.html')
    
    @staticmethod
    @sound_absorption_bp.route('/api/parts')
    @login_required
    def get_parts():
        """获取零件列表"""
        try:
            parts = sound_absorption_service.get_part_list()
            return success(parts)
        except Exception as e:
            return error(f"获取零件列表失败: {str(e)}")
    
    @staticmethod
    @sound_absorption_bp.route('/api/materials')
    @login_required
    def get_materials():
        """获取材料列表"""
        try:
            part_name = request.args.get('part_name')
            materials = sound_absorption_service.get_material_list(part_name)
            return success(materials)
        except Exception as e:
            return error(f"获取材料列表失败: {str(e)}")
    
    @staticmethod
    @sound_absorption_bp.route('/api/weights')
    @login_required
    def get_weights():
        """获取克重列表"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            
            if not part_name or not material_name:
                return bad_request("请提供零件名称和材料名称")
            
            weights = sound_absorption_service.get_weight_list(part_name, material_name)
            return success(weights)
        except Exception as e:
            return error(f"获取克重列表失败: {str(e)}")
    
    @staticmethod
    @sound_absorption_bp.route('/api/absorption_data')
    @login_required
    def get_absorption_data():
        """获取吸音系数数据"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            data = sound_absorption_service.get_absorption_data(part_name, material_name, weight)
            
            if not data:
                return error("未找到匹配的数据")
            
            return success(data, "数据获取成功")
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取吸音系数数据失败: {str(e)}")
    
    @staticmethod
    @sound_absorption_bp.route('/api/multi_weight_comparison', methods=['POST'])
    @login_required
    def get_multi_weight_comparison():
        """获取多克重对比数据"""
        try:
            data = request.get_json()
            part_name = data.get('part_name')
            material_name = data.get('material_name')
            weights = data.get('weights', [])
            
            if not all([part_name, material_name, weights]):
                return bad_request("请提供完整的对比条件")
            
            # 转换为浮点数列表
            weights = [float(w) for w in weights]
            
            comparison_data = sound_absorption_service.get_multi_weight_comparison(part_name, material_name, weights)
            return success(comparison_data, "多克重对比数据生成成功")
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"生成多克重对比数据失败: {str(e)}")
    
    @staticmethod
    @sound_absorption_bp.route('/api/test_image')
    @login_required
    def get_test_image():
        """获取测试图片信息"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            image_info = sound_absorption_service.get_test_image_info(part_name, material_name, weight)
            
            if not image_info:
                return error("未找到测试图片信息")
            
            return success(image_info)
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取测试图片信息失败: {str(e)}")
    
    @staticmethod
    @sound_absorption_bp.route('/api/export_data')
    @login_required
    def export_data():
        """导出单个数据"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            csv_content = sound_absorption_service.export_data_to_csv(part_name, material_name, weight)
            
            # 创建响应
            output = io.BytesIO()
            output.write(csv_content.encode('utf-8-sig'))  # 使用UTF-8 BOM以支持Excel
            output.seek(0)
            
            filename = f"吸音系数_{part_name}_{material_name}_{weight}g_m2.csv"
            
            return send_file(
                output,
                mimetype='text/csv',
                as_attachment=True,
                download_name=filename
            )
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"导出数据失败: {str(e)}")
    
    @staticmethod
    @sound_absorption_bp.route('/api/export_comparison', methods=['POST'])
    @login_required
    def export_comparison():
        """导出多克重对比数据"""
        try:
            data = request.get_json()
            part_name = data.get('part_name')
            material_name = data.get('material_name')
            weights = data.get('weights', [])
            
            if not all([part_name, material_name, weights]):
                return bad_request("请提供完整的对比条件")
            
            # 转换为浮点数列表
            weights = [float(w) for w in weights]
            
            csv_content = sound_absorption_service.export_comparison_data_to_csv(part_name, material_name, weights)
            
            # 创建响应
            output = io.BytesIO()
            output.write(csv_content.encode('utf-8-sig'))  # 使用UTF-8 BOM以支持Excel
            output.seek(0)
            
            weights_str = '_'.join([str(int(w)) for w in weights])
            filename = f"吸音系数对比_{part_name}_{material_name}_{weights_str}g_m2.csv"
            
            return send_file(
                output,
                mimetype='text/csv',
                as_attachment=True,
                download_name=filename
            )
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"导出对比数据失败: {str(e)}")

# 注册蓝图到应用
def register_sound_absorption_routes(app):
    """注册吸音系数路由"""
    app.register_blueprint(sound_absorption_bp)
