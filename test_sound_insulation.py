#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
吸隔声模块功能测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, SoundInsulationAreaModel, SoundInsulationDataModel, VehicleModel
from services.sound_insulation_service import SoundInsulationService

def test_models():
    """测试模型功能"""
    print("=" * 50)
    print("测试模型功能")
    print("=" * 50)
    
    with app.app_context():
        try:
            # 测试区域模型
            print("\n1. 测试区域模型...")
            areas = SoundInsulationAreaModel.get_all_areas()
            print(f"   找到 {len(areas)} 个测试区域:")
            for area in areas:
                print(f"   - {area.area_name}: {area.description}")
            
            # 测试数据模型
            print("\n2. 测试数据模型...")
            all_data = SoundInsulationDataModel.query.all()
            print(f"   找到 {len(all_data)} 条隔声量数据")
            
            # 测试频率数据获取
            if all_data:
                sample_data = all_data[0]
                freq_data = sample_data.get_frequency_data()
                print(f"   示例频率数据: {list(freq_data.keys())[:5]}... (共{len(freq_data)}个频率点)")
            
            # 测试关联查询
            print("\n3. 测试关联查询...")
            area_1_vehicles = SoundInsulationDataModel.get_vehicles_with_data_by_area(1)
            print(f"   前围区域有数据的车型: {[v.vehicle_model_name for v in area_1_vehicles]}")
            
            return True
            
        except Exception as e:
            print(f"   ✗ 模型测试失败: {e}")
            return False

def test_service():
    """测试服务层功能"""
    print("=" * 50)
    print("测试服务层功能")
    print("=" * 50)
    
    with app.app_context():
        try:
            service = SoundInsulationService()
            
            # 测试获取区域列表
            print("\n1. 测试获取区域列表...")
            areas = service.get_area_list()
            print(f"   获取到 {len(areas)} 个区域:")
            for area in areas:
                print(f"   - ID:{area['id']} {area['name']}")
            
            # 测试获取车型列表
            print("\n2. 测试获取车型列表...")
            vehicles = service.get_vehicle_list()
            print(f"   获取到 {len(vehicles)} 个车型:")
            for vehicle in vehicles:
                print(f"   - ID:{vehicle['id']} {vehicle['name']} ({vehicle['code']})")
            
            # 测试获取指定区域的车型列表
            print("\n3. 测试获取前围区域的车型列表...")
            area_vehicles = service.get_vehicle_list(area_id=1)
            print(f"   前围区域有数据的车型 {len(area_vehicles)} 个:")
            for vehicle in area_vehicles:
                print(f"   - ID:{vehicle['id']} {vehicle['name']}")
            
            # 测试生成对比数据
            print("\n4. 测试生成对比数据...")
            comparison_data = service.generate_comparison_data(area_id=1, vehicle_ids=[1, 2, 3])
            print(f"   生成对比数据成功:")
            print(f"   - 区域: {comparison_data['area_info']['name']}")
            print(f"   - 车型数量: {len(comparison_data['vehicle_info'])}")
            print(f"   - 表格数据行数: {len(comparison_data['table_data'])}")
            print(f"   - 图表系列数: {len(comparison_data['chart_data']['series'])}")
            
            # 测试导出数据
            print("\n5. 测试导出数据...")
            csv_data = service.export_comparison_data(area_id=1, vehicle_ids=[1, 2])
            print(f"   导出CSV数据成功:")
            print(f"   - 表头: {csv_data[0]}")
            print(f"   - 数据行数: {len(csv_data) - 1}")
            
            return True
            
        except Exception as e:
            print(f"   ✗ 服务层测试失败: {e}")
            return False

def test_data_integrity():
    """测试数据完整性"""
    print("=" * 50)
    print("测试数据完整性")
    print("=" * 50)
    
    with app.app_context():
        try:
            # 检查每个车型每个区域的数据完整性
            vehicles = VehicleModel.query.filter_by(status='active').all()
            areas = SoundInsulationAreaModel.get_all_areas()
            
            print(f"\n检查 {len(vehicles)} 个车型 × {len(areas)} 个区域的数据...")
            
            total_expected = len(vehicles) * len(areas)
            total_actual = SoundInsulationDataModel.query.count()
            
            print(f"   预期数据条数: {total_expected}")
            print(f"   实际数据条数: {total_actual}")
            
            # 检查每个车型的数据
            for vehicle in vehicles:
                vehicle_data_count = SoundInsulationDataModel.query.filter_by(vehicle_model_id=vehicle.id).count()
                print(f"   {vehicle.vehicle_model_name}: {vehicle_data_count} 条数据")
            
            # 检查每个区域的数据
            for area in areas:
                area_data_count = SoundInsulationDataModel.query.filter_by(area_id=area.id).count()
                print(f"   {area.area_name}: {area_data_count} 条数据")
            
            # 检查频率数据完整性
            print(f"\n检查频率数据完整性...")
            freq_columns = SoundInsulationDataModel.get_frequency_columns()
            print(f"   频率列数: {len(freq_columns)}")
            
            sample_data = SoundInsulationDataModel.query.first()
            if sample_data:
                freq_data = sample_data.get_frequency_data()
                non_null_count = sum(1 for v in freq_data.values() if v is not None)
                print(f"   示例数据非空频率点: {non_null_count}/{len(freq_data)}")
            
            return True
            
        except Exception as e:
            print(f"   ✗ 数据完整性测试失败: {e}")
            return False

def main():
    """主测试函数"""
    print("吸隔声模块功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("模型功能测试", test_models()))
    test_results.append(("服务层功能测试", test_service()))
    test_results.append(("数据完整性测试", test_data_integrity()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！吸隔声模块功能正常。")
        return True
    else:
        print("❌ 部分测试失败，请检查相关功能。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
