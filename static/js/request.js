/**
 * 统一请求客户端
 */
class RequestClient {
    constructor() {
        this.baseUrl = '';
        this.timeout = 10000;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }

    /**
     * 发送请求
     * @param {Object} config 请求配置
     */
    async request(config) {
        const {
            url,
            method = 'GET',
            data = null,
            headers = {},
            timeout = this.timeout
        } = config;

        const requestHeaders = { ...this.defaultHeaders, ...headers };
        
        const fetchConfig = {
            method: method.toUpperCase(),
            headers: requestHeaders,
            credentials: 'same-origin'
        };

        if (data && method.toUpperCase() !== 'GET') {
            if (data instanceof FormData) {
                delete fetchConfig.headers['Content-Type'];
                fetchConfig.body = data;
            } else {
                fetchConfig.body = JSON.stringify(data);
            }
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        fetchConfig.signal = controller.signal;
        
        try {
            const response = await fetch(this.baseUrl + url, fetchConfig);
            clearTimeout(timeoutId);
            
            const result = await response.json();
            
            if (result.code === 200) {
                return result;
            } else {
                const error = new Error(result.message);
                error.code = result.code;
                error.data = result.data;
                throw error;
            }
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            throw error;
        }
    }

    /**
     * GET请求
     */
    get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request({ url: fullUrl, method: 'GET' });
    }

    /**
     * POST请求
     */
    post(url, data = {}) {
        return this.request({ url, method: 'POST', data });
    }

    /**
     * PUT请求
     */
    put(url, data = {}) {
        return this.request({ url, method: 'PUT', data });
    }

    /**
     * DELETE请求
     */
    delete(url) {
        return this.request({ url, method: 'DELETE' });
    }

    /**
     * 文件上传
     */
    upload(url, formData) {
        return this.request({ 
            url, 
            method: 'POST', 
            data: formData,
            headers: {}
        });
    }
}

// 创建全局实例
const request = new RequestClient();

// 消息提示函数
function showMessage(message, type = 'info') {
    // 创建Toast元素
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${getBootstrapColor(type)} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${getIcon(type)} me-2"></i>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();
    
    // 自动移除
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed top-2 start-50 translate-middle-x';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

function getBootstrapColor(type) {
    const colorMap = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
    };
    return colorMap[type] || 'info';
}

function getIcon(type) {
    const iconMap = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return iconMap[type] || 'fa-info-circle';
}

// 全局错误处理
window.addEventListener('unhandledrejection', function(event) {
    const error = event.reason;
    if (error && error.code) {
        showMessage(error.message, 'error');
        if (error.code === 401) {
            setTimeout(() => {
                window.location.href = '/login';
            }, 1500);
        }
    }
});
