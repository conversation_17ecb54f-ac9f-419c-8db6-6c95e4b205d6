/* 吸隔声模块样式 */

/* 多选组件样式 - 继承气密性模块样式 */
.vehicle-multiselect {
    position: relative;
    width: 100%;
}

.multiselect-container {
    position: relative;
}

.multiselect-input-container {
    position: relative;
    cursor: pointer;
}

.multiselect-input {
    cursor: pointer;
    background-color: white;
    padding-right: 40px;
}

.multiselect-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.multiselect-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    transition: transform 0.2s ease;
}

.multiselect-container.open .multiselect-arrow {
    transform: translateY(-50%) rotate(180deg);
}

.multiselect-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.multiselect-container.open .multiselect-dropdown {
    display: block;
}

.multiselect-search {
    padding: 8px;
    border-bottom: 1px solid #e9ecef;
}

.multiselect-options {
    max-height: 200px;
    overflow-y: auto;
}

.multiselect-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    align-items: center;
    transition: background-color 0.15s ease;
}

.multiselect-option:hover {
    background-color: #f8f9fa;
}

.multiselect-option:last-child {
    border-bottom: none;
}

.multiselect-option input[type="checkbox"] {
    margin-right: 8px;
}

.multiselect-option.selected {
    background-color: #e7f3ff;
}

.selected-items {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.selected-item {
    display: inline-flex;
    align-items: center;
    background-color: #0d6efd;
    color: white;
    padding: 4px 8px;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    gap: 6px;
}

.selected-item .remove-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    font-size: 0.75rem;
    opacity: 0.8;
    transition: opacity 0.15s ease;
}

.selected-item .remove-btn:hover {
    opacity: 1;
}

/* 优化布局对齐 */
.vehicle-multiselect {
    min-height: 38px; /* 确保与按钮高度一致 */
}

.multiselect-input-container {
    min-height: 38px;
}

/* 确保按钮与输入框顶部对齐 */
.d-flex.align-items-start .btn {
    margin-top: 0;
}

/* 选择项标签容器固定高度，避免影响布局 */
.selected-items {
    min-height: 32px; /* 为标签预留空间 */
    margin-top: 8px !important;
}

/* 优化缩短宽度后的显示效果 */
.vehicle-multiselect .multiselect-input {
    min-width: 200px; /* 确保最小宽度能显示车型名称 */
}

.vehicle-multiselect .selected-items .selected-item {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 区域选择框样式优化 */
#area-select {
    min-width: 150px; /* 确保区域选择框最小宽度 */
}

/* 表格样式 */
#comparison-table {
    font-size: 0.9rem;
}

#comparison-table th {
    background-color: #343a40 !important;
    color: white;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
}

#comparison-table td {
    text-align: center;
    vertical-align: middle;
    padding: 8px;
}

#comparison-table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

/* 第一列频率列固定样式 */
#comparison-table th:first-child,
#comparison-table td:first-child {
    background-color: #f8f9fa;
    font-weight: 600;
    position: sticky;
    left: 0;
    z-index: 10;
}

#comparison-table th:first-child {
    background-color: #343a40 !important;
    color: white;
}

/* 图表容器样式 */
#chart-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #fff;
}

/* 测试信息表格样式 */
#test-info-table {
    font-size: 0.875rem;
}

#test-info-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-align: center;
}

#test-info-table td {
    text-align: center;
    vertical-align: middle;
}

/* 按钮样式 */
.btn-view-image {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* 空状态样式 */
#empty-state .fa-volume-up {
    color: #6c757d;
}

/* 加载指示器样式 */
#loading-indicator {
    background-color: rgba(248, 249, 250, 0.8);
    border-radius: 0.375rem;
}

/* 模态框样式 */
#imageModal .modal-body {
    padding: 1.5rem;
}

#image-container {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
}

#test-image {
    max-height: 500px;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#image-info {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    text-align: left;
}

#image-info .row {
    margin-bottom: 0.5rem;
}

#image-info .row:last-child {
    margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .multiselect-dropdown {
        position: fixed;
        top: auto;
        left: 1rem;
        right: 1rem;
        max-height: 50vh;
    }
    
    #chart-container {
        height: 300px !important;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
}

/* 图表点击效果 */
.chart-clickable {
    cursor: pointer;
}

/* 工具提示样式 */
.tooltip-custom {
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.875rem;
}
