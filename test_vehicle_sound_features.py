#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车型隔声量和混响时间功能测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, VehicleSoundInsulationModel, VehicleReverberationModel, VehicleModel
from services.sound_insulation_service import SoundInsulationService

def test_models():
    """测试数据模型功能"""
    print("=" * 50)
    print("测试数据模型功能")
    print("=" * 50)
    
    with app.app_context():
        # 测试车型隔声量模型
        print("\n1. 测试车型隔声量模型")
        insulation_data = VehicleSoundInsulationModel.query.all()
        print(f"车型隔声量数据条数: {len(insulation_data)}")
        
        if insulation_data:
            first_data = insulation_data[0]
            print(f"第一条数据: 车型ID={first_data.vehicle_model_id}")
            print(f"频率数据: {first_data.get_frequency_data()}")
            print(f"测试信息: 日期={first_data.test_date}, 工程师={first_data.test_engineer}")
        
        # 测试车型混响时间模型
        print("\n2. 测试车型混响时间模型")
        reverberation_data = VehicleReverberationModel.query.all()
        print(f"车型混响时间数据条数: {len(reverberation_data)}")
        
        if reverberation_data:
            first_data = reverberation_data[0]
            print(f"第一条数据: 车型ID={first_data.vehicle_model_id}")
            print(f"频率数据: {first_data.get_frequency_data()}")
            print(f"测试信息: 日期={first_data.test_date}, 工程师={first_data.test_engineer}")
        
        # 测试获取有数据的车型
        print("\n3. 测试获取有数据的车型")
        insulation_vehicles = VehicleSoundInsulationModel.get_vehicles_with_data()
        print(f"有隔声量数据的车型: {[v.vehicle_model_name for v in insulation_vehicles]}")
        
        reverberation_vehicles = VehicleReverberationModel.get_vehicles_with_data()
        print(f"有混响时间数据的车型: {[v.vehicle_model_name for v in reverberation_vehicles]}")

def test_services():
    """测试业务逻辑服务"""
    print("\n" + "=" * 50)
    print("测试业务逻辑服务")
    print("=" * 50)
    
    with app.app_context():
        service = SoundInsulationService()
        
        # 测试车型隔声量功能
        print("\n1. 测试车型隔声量功能")
        try:
            vehicles = service.get_vehicle_insulation_list()
            print(f"可用车型: {[v['name'] for v in vehicles]}")
            
            if vehicles:
                vehicle_ids = [v['id'] for v in vehicles[:2]]  # 取前两个车型
                print(f"测试车型ID: {vehicle_ids}")
                
                comparison_data = service.generate_vehicle_insulation_comparison(vehicle_ids)
                print(f"对比数据生成成功")
                print(f"车型信息: {[v['name'] for v in comparison_data['vehicle_info']]}")
                print(f"表格数据行数: {len(comparison_data['table_data'])}")
                print(f"图表系列数: {len(comparison_data['chart_data']['series'])}")
                
                # 测试导出功能
                csv_data = service.export_vehicle_insulation_data(vehicle_ids)
                print(f"CSV导出数据行数: {len(csv_data)}")
                
        except Exception as e:
            print(f"车型隔声量功能测试失败: {e}")
        
        # 测试车型混响时间功能
        print("\n2. 测试车型混响时间功能")
        try:
            vehicles = service.get_vehicle_reverberation_list()
            print(f"可用车型: {[v['name'] for v in vehicles]}")
            
            if vehicles:
                vehicle_ids = [v['id'] for v in vehicles[:2]]  # 取前两个车型
                print(f"测试车型ID: {vehicle_ids}")
                
                comparison_data = service.generate_vehicle_reverberation_comparison(vehicle_ids)
                print(f"对比数据生成成功")
                print(f"车型信息: {[v['name'] for v in comparison_data['vehicle_info']]}")
                print(f"表格数据行数: {len(comparison_data['table_data'])}")
                print(f"图表系列数: {len(comparison_data['chart_data']['series'])}")
                
                # 测试导出功能
                csv_data = service.export_vehicle_reverberation_data(vehicle_ids)
                print(f"CSV导出数据行数: {len(csv_data)}")
                
        except Exception as e:
            print(f"车型混响时间功能测试失败: {e}")

def test_api_endpoints():
    """测试API接口"""
    print("\n" + "=" * 50)
    print("测试API接口")
    print("=" * 50)
    
    with app.test_client() as client:
        # 模拟登录用户
        with client.session_transaction() as sess:
            sess['user'] = {'sub': 'test_user'}
            sess['user_info'] = {'username': 'test_user'}
        
        # 测试车型隔声量API
        print("\n1. 测试车型隔声量API")
        
        # 获取车型列表
        response = client.get('/sound_insulation/api/vehicle_insulation/vehicles')
        print(f"获取车型列表: {response.status_code}")
        if response.status_code == 200:
            data = response.get_json()
            print(f"车型数量: {len(data['data'])}")
        
        # 生成对比数据
        response = client.post('/sound_insulation/api/vehicle_insulation/comparison', 
                             json={'vehicle_ids': [1, 2]})
        print(f"生成对比数据: {response.status_code}")
        if response.status_code == 200:
            data = response.get_json()
            print(f"对比数据生成成功: {data['message']}")
        
        # 测试车型混响时间API
        print("\n2. 测试车型混响时间API")
        
        # 获取车型列表
        response = client.get('/sound_insulation/api/vehicle_reverberation/vehicles')
        print(f"获取车型列表: {response.status_code}")
        if response.status_code == 200:
            data = response.get_json()
            print(f"车型数量: {len(data['data'])}")
        
        # 生成对比数据
        response = client.post('/sound_insulation/api/vehicle_reverberation/comparison', 
                             json={'vehicle_ids': [1, 2]})
        print(f"生成对比数据: {response.status_code}")
        if response.status_code == 200:
            data = response.get_json()
            print(f"对比数据生成成功: {data['message']}")

def test_page_routes():
    """测试页面路由"""
    print("\n" + "=" * 50)
    print("测试页面路由")
    print("=" * 50)
    
    with app.test_client() as client:
        # 模拟登录用户
        with client.session_transaction() as sess:
            sess['user'] = {'sub': 'test_user'}
            sess['user_info'] = {'username': 'test_user'}
        
        # 测试车型隔声量页面
        response = client.get('/sound_insulation/vehicle_insulation')
        print(f"车型隔声量页面: {response.status_code}")
        if response.status_code == 200:
            print("页面加载成功")
        
        # 测试车型混响时间页面
        response = client.get('/sound_insulation/vehicle_reverberation')
        print(f"车型混响时间页面: {response.status_code}")
        if response.status_code == 200:
            print("页面加载成功")

def main():
    """主测试函数"""
    print("开始测试车型隔声量和混响时间功能")
    print("测试时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    try:
        # 测试数据模型
        test_models()
        
        # 测试业务逻辑
        test_services()
        
        # 测试API接口
        test_api_endpoints()
        
        # 测试页面路由
        test_page_routes()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
