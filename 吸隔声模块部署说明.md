# 吸隔声模块部署说明

## 概述

吸隔声模块已成功实现，提供区域隔声量ATF对比分析功能。本文档说明模块的部署和使用方法。

## 功能特性

### 1. 核心功能
- **区域隔声量对比**: 支持不同车型在同一区域的隔声量对比分析
- **多车型选择**: 支持同时选择多个车型进行对比
- **频率范围**: 覆盖200Hz-10000Hz共18个中心频率点
- **可视化图表**: 使用ECharts生成交互式折线图
- **测试图片查看**: 点击图表或按钮查看测试附图
- **数据导出**: 支持CSV格式数据导出

### 2. 测试区域
- 前围
- 动力总成  
- 前挡
- 后挡
- 左前门
- 右前门
- 左后门
- 右后门

### 3. 界面特性
- 响应式设计，适配不同屏幕尺寸
- 继承气密性模块的UI风格，保持系统一致性
- 支持表格横向滚动，频率列固定显示
- 交互式图表，支持悬停显示详细数值

## 文件结构

### 后端文件
```
models/
├── sound_insulation_area_model.py      # 区域模型
├── sound_insulation_data_model.py      # 数据模型
└── __init__.py                         # 已更新

services/
└── sound_insulation_service.py         # 业务逻辑服务

controllers/
└── sound_insulation_controller.py      # 控制器
```

### 前端文件
```
templates/sound_insulation/
└── area_comparison.html                # 区域隔声量对比页面

static/css/
└── sound_insulation.css               # 样式文件

static/js/
└── sound_insulation.js                # 交互逻辑
```

### 配置文件
```
app.py                                  # 已更新，注册蓝图
templates/base.html                     # 已更新，添加导航菜单
templates/index.html                    # 已更新，添加入口卡片
```

## 数据库结构

### 1. 区域表 (sound_insulation_areas)
```sql
CREATE TABLE sound_insulation_areas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    area_name VARCHAR(50) NOT NULL COMMENT '区域名称',
    description TEXT COMMENT '区域描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 隔声量数据表 (sound_insulation_data)
```sql
CREATE TABLE sound_insulation_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_model_id INT NOT NULL COMMENT '车型ID',
    area_id INT NOT NULL COMMENT '区域ID',
    -- 18个频率字段 (freq_200 到 freq_10000)
    freq_200 DECIMAL(5,2) COMMENT '200Hz隔声量(dB)',
    -- ... 其他频率字段
    freq_10000 DECIMAL(5,2) COMMENT '10000Hz隔声量(dB)',
    -- 测试信息字段
    test_image_path VARCHAR(500) COMMENT '测试图片路径',
    test_date DATE COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) COMMENT '测试工程师',
    remarks TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_model_id) REFERENCES vehicle_models(id),
    FOREIGN KEY (area_id) REFERENCES sound_insulation_areas(id)
);
```

## 部署步骤

### 1. 数据导入
测试数据已通过 `import_sound_insulation_data.py` 脚本成功导入：
- 8个测试区域
- 12条隔声量测试数据（3个车型 × 4个区域）
- 覆盖宝骏530、宝骏510、五菱宏光MINI EV三个车型

### 2. 应用启动
应用程序已成功启动并运行在 http://127.0.0.1:5000

### 3. 功能测试
所有功能测试已通过：
- ✓ 模型功能测试
- ✓ 服务层功能测试  
- ✓ 数据完整性测试

## 使用说明

### 1. 访问入口
- **导航菜单**: 左侧菜单 → 吸隔声模块 → 区域隔声量
- **首页入口**: 首页卡片 → 吸隔声模块 → 进入查询
- **直接访问**: http://localhost:5000/sound_insulation/area_comparison

### 2. 操作流程
1. **选择区域**: 从下拉框中选择测试区域（单选）
2. **选择车型**: 从多选框中选择一个或多个车型
3. **生成对比**: 点击"生成对比表"按钮
4. **查看结果**: 
   - 查看数据表格
   - 查看折线对比图
   - 查看测试信息
5. **查看附图**: 点击图表线条或"查看附图"按钮
6. **导出数据**: 点击"导出数据"按钮下载CSV文件

### 3. 界面说明
- **查询条件区域**: 区域选择和车型多选
- **数据表格**: 频率固定列 + 车型数据列，支持横向滚动
- **折线图**: 交互式图表，支持悬停和点击
- **测试信息表**: 显示各车型的测试详情
- **图片模态框**: 显示测试附图和详细信息

## API接口

### 1. 获取区域列表
```
GET /sound_insulation/api/areas
```

### 2. 获取车型列表
```
GET /sound_insulation/api/vehicles?area_id={area_id}
```

### 3. 生成对比数据
```
POST /sound_insulation/api/comparison
Body: {
    "area_id": 1,
    "vehicle_ids": [1, 2, 3]
}
```

### 4. 获取测试图片
```
GET /sound_insulation/api/test_image?vehicle_id={vehicle_id}&area_id={area_id}
```

### 5. 导出数据
```
POST /sound_insulation/api/export
Body: {
    "area_id": 1,
    "vehicle_ids": [1, 2]
}
```

## 技术特点

### 1. 架构设计
- **MVC架构**: 模型-视图-控制器分离
- **服务层**: 业务逻辑封装
- **蓝图模式**: 模块化路由管理

### 2. 前端技术
- **Bootstrap 5**: 响应式UI框架
- **ECharts 5**: 数据可视化图表库
- **原生JavaScript**: 交互逻辑实现
- **CSS3**: 自定义样式和动画

### 3. 后端技术
- **Flask**: Web框架
- **SQLAlchemy**: ORM数据库操作
- **MySQL**: 关系型数据库
- **Python**: 服务端语言

## 扩展建议

### 1. 功能扩展
- 添加更多测试区域
- 支持频率范围自定义
- 增加统计分析功能
- 支持批量数据导入

### 2. 性能优化
- 数据缓存机制
- 图表渲染优化
- 大数据量分页处理

### 3. 用户体验
- 数据筛选功能
- 图表样式自定义
- 移动端适配优化

## 故障排除

### 1. 常见问题
- **数据不显示**: 检查数据库连接和数据完整性
- **图表不渲染**: 检查ECharts库加载和数据格式
- **导出失败**: 检查服务器权限和磁盘空间

### 2. 调试方法
- 使用 `test_sound_insulation.py` 进行功能测试
- 检查浏览器控制台错误信息
- 查看Flask应用日志

## 总结

吸隔声模块已成功实现并部署，提供了完整的区域隔声量对比分析功能。模块采用现代化的技术架构，具有良好的可扩展性和用户体验。所有核心功能均已测试通过，可以投入使用。
