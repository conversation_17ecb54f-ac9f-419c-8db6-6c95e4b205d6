#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库数据
"""

import pymysql
from config import Config

def check_database():
    """检查数据库数据"""
    try:
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查材料表数据
            print("=== 材料表数据 ===")
            cursor.execute("SELECT * FROM materials")
            materials = cursor.fetchall()
            if materials:
                for material in materials:
                    print(f"ID: {material[0]}, 名称: {material[1]}, 厚度: {material[2]}, 克重: {material[3]}")
            else:
                print("材料表为空，手动插入数据...")
                # 手动插入材料数据
                material_data = [
                    ('PET纤维毡', 10.0, 800, '聚酯纤维毡材料'),
                    ('PET纤维毡', 15.0, 1200, '聚酯纤维毡材料'),
                    ('PET纤维毡', 20.0, 1600, '聚酯纤维毡材料'),
                    ('玻璃纤维毡', 12.0, 1000, '玻璃纤维毡材料'),
                    ('玻璃纤维毡', 18.0, 1500, '玻璃纤维毡材料'),
                    ('聚氨酯泡沫', 25.0, 600, '聚氨酯发泡材料'),
                    ('聚氨酯泡沫', 30.0, 800, '聚氨酯发泡材料')
                ]
                
                # 由于有唯一约束，我们需要先删除约束或者使用不同的材料名
                for i, (name, thickness, weight, desc) in enumerate(material_data):
                    try:
                        unique_name = f"{name}_{thickness}mm_{weight}g"
                        cursor.execute(
                            "INSERT INTO materials (material_name, thickness, weight, description) VALUES (%s, %s, %s, %s)",
                            (unique_name, thickness, weight, desc)
                        )
                        print(f"✓ 插入材料: {unique_name}")
                    except Exception as e:
                        print(f"✗ 插入材料失败: {e}")
                
                connection.commit()
            
            print("\n=== 零件表数据 ===")
            cursor.execute("SELECT * FROM sound_insulation_parts")
            parts = cursor.fetchall()
            for part in parts:
                print(f"ID: {part[0]}, 名称: {part[1]}")
            
            print("\n=== 厂家表数据 ===")
            cursor.execute("SELECT * FROM material_manufacturers")
            manufacturers = cursor.fetchall()
            for manufacturer in manufacturers:
                print(f"ID: {manufacturer[0]}, 名称: {manufacturer[1]}")
            
            print("\n=== 吸声系数表数据 ===")
            cursor.execute("SELECT part_name, material_name, weight FROM sound_absorption_coefficients")
            coefficients = cursor.fetchall()
            for coeff in coefficients:
                print(f"零件: {coeff[0]}, 材料: {coeff[1]}, 克重: {coeff[2]}")
        
        connection.close()
        
    except Exception as e:
        print(f"✗ 检查数据库失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_database()
