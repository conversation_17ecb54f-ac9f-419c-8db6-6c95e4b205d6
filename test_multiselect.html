<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多选框测试页面</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="static/css/modal_search.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>自定义多选框测试</h2>
        
        <div class="row mt-4">
            <div class="col-md-4">
                <h4>车型选择</h4>
                <div class="custom-multiselect" id="test-vehicles-multiselect">
                    <div class="multiselect-trigger">
                        <span class="multiselect-placeholder">请选择车型</span>
                        <span class="multiselect-count">已选: 0</span>
                        <i class="multiselect-arrow fas fa-chevron-down"></i>
                    </div>
                    <div class="multiselect-dropdown">
                        <div class="vehicle-search-container" style="padding: 8px; border-bottom: 1px solid #e9ecef;">
                            <input type="text" class="form-control form-control-sm" id="vehicle-search" placeholder="搜索车型...">
                        </div>
                        <div id="vehicle-options-container">
                            <div class="multiselect-option" data-value="model1">
                                <i class="option-checkbox far fa-square"></i>
                                <span class="option-text">宝骏530</span>
                            </div>
                            <div class="multiselect-option" data-value="model2">
                                <i class="option-checkbox far fa-square"></i>
                                <span class="option-text">五菱宏光MINI EV</span>
                            </div>
                            <div class="multiselect-option" data-value="model3">
                                <i class="option-checkbox far fa-square"></i>
                                <span class="option-text">新宝骏RS-5</span>
                            </div>
                            <div class="multiselect-option" data-value="model4">
                                <i class="option-checkbox far fa-square"></i>
                                <span class="option-text">五菱凯捷</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <h4>测试状态选择</h4>
                <div class="custom-multiselect" id="test-conditions-multiselect">
                    <div class="multiselect-trigger">
                        <span class="multiselect-placeholder">请选择测试状态</span>
                        <span class="multiselect-count">已选: 0</span>
                        <i class="multiselect-arrow fas fa-chevron-down"></i>
                    </div>
                    <div class="multiselect-dropdown">
                        <div class="multiselect-option" data-value="static">
                            <i class="option-checkbox far fa-square"></i>
                            <span class="option-text">静态测试</span>
                        </div>
                        <div class="multiselect-option" data-value="dynamic">
                            <i class="option-checkbox far fa-square"></i>
                            <span class="option-text">动态测试</span>
                        </div>
                        <div class="multiselect-option" data-value="fatigue">
                            <i class="option-checkbox far fa-square"></i>
                            <span class="option-text">疲劳测试</span>
                        </div>
                        <div class="multiselect-option" data-value="impact">
                            <i class="option-checkbox far fa-square"></i>
                            <span class="option-text">冲击测试</span>
                        </div>
                        <div class="multiselect-option" data-value="vibration">
                            <i class="option-checkbox far fa-square"></i>
                            <span class="option-text">振动测试</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <h4>模态振型选择</h4>
                <div class="custom-multiselect" id="test-modes-multiselect">
                    <div class="multiselect-trigger">
                        <span class="multiselect-placeholder">请选择模态振型</span>
                        <span class="multiselect-count">已选: 0</span>
                        <i class="multiselect-arrow fas fa-chevron-down"></i>
                    </div>
                    <div class="multiselect-dropdown">
                        <div class="multiselect-option" data-value="mode1">
                            <i class="option-checkbox far fa-square"></i>
                            <span class="option-text">第一阶弯曲模态</span>
                        </div>
                        <div class="multiselect-option" data-value="mode2">
                            <i class="option-checkbox far fa-square"></i>
                            <span class="option-text">第二阶弯曲模态</span>
                        </div>
                        <div class="multiselect-option" data-value="mode3">
                            <i class="option-checkbox far fa-square"></i>
                            <span class="option-text">第一阶扭转模态</span>
                        </div>
                        <div class="multiselect-option" data-value="mode4">
                            <i class="option-checkbox far fa-square"></i>
                            <span class="option-text">第二阶扭转模态</span>
                        </div>
                        <div class="multiselect-option" data-value="mode5">
                            <i class="option-checkbox far fa-square"></i>
                            <span class="option-text">局部振动模态</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h4>选择结果</h4>
                <div id="result-display" class="alert alert-info">
                    请选择车型、测试状态和模态振型
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 简化的测试脚本
        class TestMultiselect {
            constructor() {
                this.selectedVehicles = new Set();
                this.selectedConditions = new Set();
                this.selectedModes = new Set();
                this.init();
            }

            init() {
                this.initMultiselect(document.getElementById('test-vehicles-multiselect'), 'vehicles');
                this.initMultiselect(document.getElementById('test-conditions-multiselect'), 'conditions');
                this.initMultiselect(document.getElementById('test-modes-multiselect'), 'modes');
            }
            
            initMultiselect(container, type) {
                const trigger = container.querySelector('.multiselect-trigger');
                const dropdown = container.querySelector('.multiselect-dropdown');
                
                // 点击触发器展开/折叠
                trigger.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleMultiselect(container);
                });
                
                // 点击选项
                dropdown.addEventListener('click', (e) => {
                    const option = e.target.closest('.multiselect-option');
                    if (option) {
                        e.stopPropagation();
                        this.toggleOption(container, option.dataset.value, type);
                    }
                });
                
                // 点击外部关闭
                document.addEventListener('click', (e) => {
                    if (!container.contains(e.target)) {
                        this.closeMultiselect(container);
                    }
                });
            }
            
            toggleMultiselect(container) {
                const dropdown = container.querySelector('.multiselect-dropdown');
                const arrow = container.querySelector('.multiselect-arrow');
                const trigger = container.querySelector('.multiselect-trigger');
                
                if (dropdown.classList.contains('show')) {
                    this.closeMultiselect(container);
                } else {
                    // 关闭其他多选框
                    document.querySelectorAll('.custom-multiselect .multiselect-dropdown.show').forEach(dd => {
                        dd.classList.remove('show');
                        dd.parentElement.querySelector('.multiselect-arrow').classList.remove('rotated');
                        dd.parentElement.querySelector('.multiselect-trigger').classList.remove('active');
                    });
                    
                    // 打开当前多选框
                    dropdown.classList.add('show');
                    arrow.classList.add('rotated');
                    trigger.classList.add('active');
                }
            }
            
            closeMultiselect(container) {
                const dropdown = container.querySelector('.multiselect-dropdown');
                const arrow = container.querySelector('.multiselect-arrow');
                const trigger = container.querySelector('.multiselect-trigger');
                
                dropdown.classList.remove('show');
                arrow.classList.remove('rotated');
                trigger.classList.remove('active');
            }
            
            toggleOption(container, value, type) {
                let selectedSet;
                if (type === 'vehicles') {
                    selectedSet = this.selectedVehicles;
                } else if (type === 'conditions') {
                    selectedSet = this.selectedConditions;
                } else {
                    selectedSet = this.selectedModes;
                }

                const option = container.querySelector(`[data-value="${value}"]`);
                const checkbox = option.querySelector('.option-checkbox');

                if (selectedSet.has(value)) {
                    selectedSet.delete(value);
                    option.classList.remove('selected');
                    checkbox.className = 'option-checkbox far fa-square';
                } else {
                    selectedSet.add(value);
                    option.classList.add('selected');
                    checkbox.className = 'option-checkbox fas fa-check-square';
                }

                this.updateDisplay(container, type);
                this.updateResult();
            }

            updateDisplay(container, type) {
                const count = container.querySelector('.multiselect-count');
                let selectedSet;
                if (type === 'vehicles') {
                    selectedSet = this.selectedVehicles;
                } else if (type === 'conditions') {
                    selectedSet = this.selectedConditions;
                } else {
                    selectedSet = this.selectedModes;
                }
                count.textContent = `已选: ${selectedSet.size}`;
            }

            updateResult() {
                const resultDiv = document.getElementById('result-display');
                const vehicles = Array.from(this.selectedVehicles);
                const conditions = Array.from(this.selectedConditions);
                const modes = Array.from(this.selectedModes);

                resultDiv.innerHTML = `
                    <strong>车型:</strong> ${vehicles.length > 0 ? vehicles.join(', ') : '未选择'}<br>
                    <strong>测试状态:</strong> ${conditions.length > 0 ? conditions.join(', ') : '未选择'}<br>
                    <strong>模态振型:</strong> ${modes.length > 0 ? modes.join(', ') : '未选择'}
                `;
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            new TestMultiselect();
        });
    </script>
</body>
</html>
