#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复材料表结构和数据
"""

import pymysql
from config import Config

def fix_materials_table():
    """修复材料表"""
    try:
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 删除唯一约束
            try:
                cursor.execute("ALTER TABLE materials DROP INDEX uk_material_name")
                print("✓ 删除材料表唯一约束")
            except Exception as e:
                print(f"⚠ 删除约束失败（可能不存在）: {e}")
            
            # 清空材料表
            cursor.execute("DELETE FROM materials")
            print("✓ 清空材料表")
            
            # 插入正确的材料数据
            material_data = [
                ('PET纤维毡', 10.0, 800, '聚酯纤维毡材料'),
                ('PET纤维毡', 15.0, 1200, '聚酯纤维毡材料'),
                ('PET纤维毡', 20.0, 1600, '聚酯纤维毡材料'),
                ('玻璃纤维毡', 12.0, 1000, '玻璃纤维毡材料'),
                ('玻璃纤维毡', 18.0, 1500, '玻璃纤维毡材料'),
                ('聚氨酯泡沫', 25.0, 600, '聚氨酯发泡材料'),
                ('聚氨酯泡沫', 30.0, 800, '聚氨酯发泡材料')
            ]
            
            for name, thickness, weight, desc in material_data:
                cursor.execute(
                    "INSERT INTO materials (material_name, thickness, weight, description) VALUES (%s, %s, %s, %s)",
                    (name, thickness, weight, desc)
                )
                print(f"✓ 插入材料: {name} {thickness}mm {weight}g/m²")
            
            connection.commit()
            print("✓ 材料表修复完成")
            
            # 验证数据
            cursor.execute("SELECT material_name, thickness, weight FROM materials ORDER BY material_name, weight")
            materials = cursor.fetchall()
            print("\n=== 材料表数据 ===")
            for material in materials:
                print(f"材料: {material[0]}, 厚度: {material[1]}mm, 克重: {material[2]}g/m²")
        
        connection.close()
        
    except Exception as e:
        print(f"✗ 修复材料表失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    fix_materials_table()
