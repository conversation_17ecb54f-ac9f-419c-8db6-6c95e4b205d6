#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化车型隔声量和混响时间数据库表
"""

from app import app
from models import db

def init_database():
    """初始化数据库"""
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            print("✓ 数据库表创建成功")
            
            # 检查新表是否创建成功
            from models import VehicleSoundInsulationModel, VehicleReverberationModel
            
            # 检查车型隔声量表
            insulation_count = VehicleSoundInsulationModel.query.count()
            print(f"✓ 车型隔声量数据表已创建，当前数据条数: {insulation_count}")
            
            # 检查车型混响时间表
            reverberation_count = VehicleReverberationModel.query.count()
            print(f"✓ 车型混响时间数据表已创建，当前数据条数: {reverberation_count}")
            
            print("\n数据库初始化完成！")
            print("请手动执行 create_vehicle_sound_tables.sql 中的INSERT语句来添加测试数据")
            
        except Exception as e:
            print(f"✗ 数据库初始化失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    init_database()
