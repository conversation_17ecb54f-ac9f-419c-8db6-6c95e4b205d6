#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试垂直入射法吸音系数功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db
from models.sound_absorption_models import (
    SoundInsulationPartModel,
    MaterialModel,
    MaterialManufacturerModel,
    SoundAbsorptionCoefficientModel
)

def test_models():
    """测试模型"""
    print("=== 测试数据模型 ===")
    
    with app.app_context():
        try:
            # 测试零件模型
            parts = SoundInsulationPartModel.get_all_parts()
            print(f"✓ 零件模型测试成功，共 {len(parts)} 个零件")
            for part in parts:
                print(f"  - {part.part_name}")
            
            # 测试材料模型
            materials = MaterialModel.get_all_materials()
            print(f"✓ 材料模型测试成功，共 {len(materials)} 个材料")
            for material in materials:
                print(f"  - {material.material_name} ({material.thickness}mm, {material.weight}g/m²)")
            
            # 测试厂家模型
            manufacturers = MaterialManufacturerModel.get_all_manufacturers()
            print(f"✓ 厂家模型测试成功，共 {len(manufacturers)} 个厂家")
            
            # 测试吸声系数模型
            coefficients = SoundAbsorptionCoefficientModel.query.all()
            print(f"✓ 吸声系数模型测试成功，共 {len(coefficients)} 条数据")
            for coeff in coefficients:
                print(f"  - {coeff.part_name} - {coeff.material_name} - {coeff.weight}g/m²")
            
            # 测试业务方法
            weights = SoundAbsorptionCoefficientModel.get_weights_by_part_material('前围隔音垫', 'PET纤维毡')
            print(f"✓ 克重查询测试成功，前围隔音垫-PET纤维毡有 {len(weights)} 个克重")
            
            # 测试数据查询
            data = SoundAbsorptionCoefficientModel.get_data_by_conditions('前围隔音垫', 'PET纤维毡', 800)
            if data:
                print("✓ 数据查询测试成功")
                freq_data = data.get_test_frequency_data()
                print(f"  - 125Hz测试值: {freq_data.get('125Hz')}")
                print(f"  - 1000Hz测试值: {freq_data.get('1000Hz')}")
            else:
                print("✗ 数据查询测试失败")
            
        except Exception as e:
            print(f"✗ 模型测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_services():
    """测试服务层"""
    print("\n=== 测试业务服务 ===")
    
    try:
        from services.sound_absorption_service import SoundAbsorptionService
        service = SoundAbsorptionService()
        
        with app.app_context():
            # 测试获取零件列表
            parts = service.get_part_list()
            print(f"✓ 获取零件列表成功，共 {len(parts)} 个零件")
            
            # 测试获取材料列表
            materials = service.get_material_list('前围隔音垫')
            print(f"✓ 获取材料列表成功，前围隔音垫有 {len(materials)} 种材料")
            
            # 测试获取克重列表
            weights = service.get_weight_list('前围隔音垫', 'PET纤维毡')
            print(f"✓ 获取克重列表成功，前围隔音垫-PET纤维毡有 {len(weights)} 个克重")
            
            # 测试获取吸音系数数据
            data = service.get_absorption_data('前围隔音垫', 'PET纤维毡', 800)
            if data:
                print("✓ 获取吸音系数数据成功")
                print(f"  - 基础信息: {data['basic_info']['part_name']} - {data['basic_info']['material_name']}")
                print(f"  - 表格数据行数: {len(data['table_data'])}")
                print(f"  - 图表频率点数: {len(data['chart_data']['frequencies'])}")
            else:
                print("✗ 获取吸音系数数据失败")
            
    except Exception as e:
        print(f"✗ 服务测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_routes():
    """测试路由"""
    print("\n=== 测试路由 ===")
    
    try:
        with app.test_client() as client:
            # 测试页面路由
            response = client.get('/sound_absorption/coefficient_query')
            print(f"✓ 页面路由测试: {response.status_code}")
            
            # 测试API路由
            response = client.get('/sound_absorption/api/parts')
            print(f"✓ 零件API路由测试: {response.status_code}")
            
            response = client.get('/sound_absorption/api/materials?part_name=前围隔音垫')
            print(f"✓ 材料API路由测试: {response.status_code}")
            
            response = client.get('/sound_absorption/api/weights?part_name=前围隔音垫&material_name=PET纤维毡')
            print(f"✓ 克重API路由测试: {response.status_code}")
            
            response = client.get('/sound_absorption/api/absorption_data?part_name=前围隔音垫&material_name=PET纤维毡&weight=800')
            print(f"✓ 数据API路由测试: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 路由测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🚀 开始测试垂直入射法吸音系数功能")
    
    test_models()
    test_services()
    test_routes()
    
    print("\n🎉 功能测试完成！")

if __name__ == '__main__':
    main()
