#!/usr/bin/env python3
"""
气密性模块测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    try:
        print("测试模块导入...")
        
        # 测试模型导入
        from models.airtightness_test_model import AirtightnessTestModel
        from models.airtightness_image_model import AirtightnessImageModel
        print("✓ 模型导入成功")
        
        # 测试服务导入
        from services.airtightness_service import AirtightnessService
        print("✓ 服务导入成功")
        
        # 测试控制器导入
        from controllers.airtightness_controller import airtightness_bp
        print("✓ 控制器导入成功")
        
        print("所有模块导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_area_config():
    """测试区域配置"""
    try:
        print("\n测试区域配置...")
        from models.airtightness_test_model import AirtightnessTestModel
        
        config = AirtightnessTestModel.get_area_config()
        
        expected_categories = ['整车不可控泄漏量', '阀系统', '门系统', '白车身', '其他区域']
        
        for category in expected_categories:
            if category not in config:
                print(f"✗ 缺少区域分类: {category}")
                return False
            print(f"✓ 区域分类 '{category}' 存在")
        
        # 检查阀系统的子项
        valve_items = config['阀系统']
        expected_valve_items = ['左侧泄压阀', '右侧泄压阀', '空调内外循环阀']
        
        for item in expected_valve_items:
            found = any(valve_item['name'] == item for valve_item in valve_items)
            if not found:
                print(f"✗ 阀系统缺少子项: {item}")
                return False
            print(f"✓ 阀系统子项 '{item}' 存在")
        
        print("区域配置测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 区域配置测试失败: {e}")
        return False

def test_service_methods():
    """测试服务方法"""
    try:
        print("\n测试服务方法...")
        from services.airtightness_service import AirtightnessService
        
        service = AirtightnessService()
        
        # 测试方法是否存在
        methods = ['get_vehicle_list', 'get_all_vehicles', 'generate_comparison_data', 
                  'get_vehicle_images', 'export_comparison_data']
        
        for method_name in methods:
            if not hasattr(service, method_name):
                print(f"✗ 服务缺少方法: {method_name}")
                return False
            print(f"✓ 服务方法 '{method_name}' 存在")
        
        print("服务方法测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 服务方法测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始气密性模块测试...\n")
    
    tests = [
        test_imports,
        test_area_config,
        test_service_methods
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！气密性模块代码结构正确。")
        return True
    else:
        print("❌ 部分测试失败，请检查代码。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
