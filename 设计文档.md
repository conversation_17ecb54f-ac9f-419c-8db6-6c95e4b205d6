markdown path=设计文档.md mode=EDIT
# NVH数据管理系统设计文档（模态数据模块）

## 1. 系统架构设计

### 1.1 技术架构
- **后端框架**: Flask + SQLAlchemy
- **前端技术**: HTML + JavaScript + CSS + Jinja2
- **数据库**: MySQL
- **认证系统**: Keycloak (已完成)
- **部署**: 单体应用架构

### 1.2 项目结构
```
nvh_system/
├── app.py                    # 应用入口
├── config.py                 # 配置文件
├── decorators.py            # 装饰器（认证等）
├── requirements.txt         # 依赖包
├── models/                  # 数据模型（一个表一个文件）
│   ├── __init__.py
│   ├── base_model.py        # 基础模型类
│   ├── vehicle_model.py     # 车型模型
│   ├── component_model.py   # 零部件模型
│   ├── test_project_model.py # 测试项目模型
│   └── modal_data_model.py  # 模态数据模型
├── controllers/             # 控制器
│   ├── __init__.py
│   ├── auth_controller.py   # 认证控制器
│   └── modal_controller.py  # 模态数据控制器
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── modal_service.py    # 模态数据业务逻辑
│   └── file_service.py     # 文件管理服务
├── utils/                  # 工具类
│   ├── __init__.py
│   ├── result.py          # 统一返回结果
│   ├── validators.py      # 数据验证
│   └── helpers.py         # 辅助函数
├── static/                # 静态文件
│   ├── css/
│   │   ├── base.css       # 基础样式
│   │   ├── modal_search.css # 模态搜索页面样式
│   │   └── modal_detail.css # 模态详情页面样式
│   ├── js/
│   │   ├── base.js        # 基础JS
│   │   ├── modal_search.js # 模态搜索页面JS
│   │   ├── modal_detail.js # 模态详情页面JS
│   │   ├── utils.js       # 工具函数
│   │   └── request.js     # 统一请求封装
│   ├── images/            # 图片资源
│   └── uploads/           # 上传文件存储
│       ├── modal_shapes/  # 模态振型GIF文件
│       └── test_photos/   # 测试照片
└── templates/             # 模板文件
    ├── base.html          # 基础模板
    ├── index.html         # 首页
    ├── 404.html           # 404错误页面
    ├── 500.html           # 500错误页面
    └── modal/             # 模态数据相关模板
        ├── modal_search.html    # 搜索页面
        └── modal_detail.html    # 详情弹窗
```

## 2. 命名规范

### 2.1 Python后端命名规范
- **文件/目录**: 小写+下划线 (`modal_data_model.py`)
- **类名**: 大驼峰 (`ModalDataModel`)
- **函数/变量**: 小写+下划线 (`get_modal_data`)
- **常量**: 大写+下划线 (`MAX_UPLOAD_SIZE`)
- **数据库表名**: 小写+下划线 (`modal_data`)
- **数据库字段**: 小写+下划线 (`mode_order`)

### 2.2 前端命名规范
- **HTML文件**: 小写+下划线 (`modal_search.html`)
- **CSS文件**: 小写+下划线 (`modal_search.css`)
- **JS文件**: 小写+下划线 (`modal_search.js`)
- **CSS类名**: 小写+连字符 (`modal-search-form`)
- **JS变量/函数**: 小驼峰 (`modalSearchForm`)
- **JS常量**: 大写+下划线 (`API_BASE_URL`)

### 2.3 文件对应关系
```
templates/modal/modal_search.html
static/css/modal_search.css
static/js/modal_search.js

templates/modal/modal_detail.html
static/css/modal_detail.css
static/js/modal_detail.js
```

## 3. 数据模型设计

### 3.1 基础模型类
```python
# models/base_model.py
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class BaseModel(db.Model):
    """基础模型类"""
    __abstract__ = True
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.strftime('%Y-%m-%d %H:%M:%S')
            result[column.name] = value
        return result
```

### 3.2 车型模型
```python
# models/vehicle_model.py
from models.base_model import BaseModel, db

class VehicleModel(BaseModel):
    """车型模型"""
    __tablename__ = 'vehicle_models'
    
    vehicle_model_code = db.Column(db.String(50), unique=True, nullable=False, comment='车型代码')
    vehicle_model_name = db.Column(db.String(100), nullable=False, comment='车型名称')
    vin = db.Column(db.String(50), unique=True, nullable=False, comment='VIN码')
    drive_type = db.Column(db.String(30), comment='驱动类型')
    configuration = db.Column(db.String(200), comment='具体配置')
    production_year = db.Column(db.Integer, comment='生产年份')
    status = db.Column(db.Enum('active', 'inactive'), default='active', comment='状态')
```

### 3.3 零部件模型
```python
# models/component_model.py
from models.base_model import BaseModel, db

class ComponentModel(BaseModel):
    """零部件模型"""
    __tablename__ = 'components'
    
    component_code = db.Column(db.String(50), unique=True, nullable=False, comment='零件代码')
    component_name = db.Column(db.String(100), nullable=False, comment='零件名称')
    category = db.Column(db.String(100), nullable=False, comment='主分类')
    sub_category = db.Column(db.String(50), nullable=False, comment='子分类')
    parent_id = db.Column(db.Integer, db.ForeignKey('components.id'), comment='父级零件ID')
    description = db.Column(db.Text, comment='描述')
    material = db.Column(db.String(100), comment='材料')
    weight = db.Column(db.Numeric(8, 3), comment='重量(kg)')
```

### 3.4 测试项目模型
```python
# models/test_project_model.py
from models.base_model import BaseModel, db

class TestProjectModel(BaseModel):
    """测试项目模型"""
    __tablename__ = 'test_projects'
    
    project_code = db.Column(db.String(50), unique=True, nullable=False, comment='项目代码')
    project_name = db.Column(db.String(100), nullable=False, comment='项目名称')
    vehicle_model_id = db.Column(db.Integer, db.ForeignKey('vehicle_models.id'), nullable=False, comment='车辆ID')
    component_id = db.Column(db.Integer, db.ForeignKey('components.id'), comment='零件ID')
    test_type = db.Column(db.String(200), nullable=False, comment='测试类型')
    test_date = db.Column(db.Date, nullable=False, comment='测试日期')
    test_location = db.Column(db.String(100), comment='测试地点')
    test_engineer = db.Column(db.String(50), nullable=False, comment='测试工程师')
    test_condition = db.Column(db.String(200), comment='测试条件')
    test_status = db.Column(db.String(200), comment='测试状态')
    excitation_method = db.Column(db.String(100), comment='激励方式')
    notes = db.Column(db.Text, comment='备注')
```

### 3.5 模态数据模型
```python
# models/modal_data_model.py
from models.base_model import BaseModel, db

class ModalDataModel(BaseModel):
    """模态数据模型"""
    __tablename__ = 'modal_data'
    
    test_project_id = db.Column(db.Integer, db.ForeignKey('test_projects.id'), nullable=False, comment='测试项目ID')
    mode_order = db.Column(db.Integer, nullable=False, comment='模态阶次')
    direction = db.Column(db.String(100), comment='方向')
    frequency = db.Column(db.Numeric(10, 2), nullable=False, comment='频率(Hz)')
    damping_ratio = db.Column(db.Numeric(6, 2), comment='阻尼比')
    mode_shape_description = db.Column(db.Text, comment='模态振型描述')
    mode_shape_file = db.Column(db.String(255), comment='GIF动图文件路径')
    test_photo_file = db.Column(db.String(255), comment='测试照片文件路径')
    notes = db.Column(db.Text, comment='备注')
    updated_by = db.Column(db.String(50), comment='修改人员')
```

## 4. 控制器设计

### 4.1 认证控制器
```python
# controllers/auth_controller.py
from flask import Blueprint, request, session, redirect, url_for
from utils.result import success, error, unauthorized

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

class AuthController:
    """认证控制器"""
    
    # 自动认证模式，无需登录页面
    
    @staticmethod
    @auth_bp.route('/callback')
    def callback():
        """OAuth回调处理"""
        # Keycloak回调处理逻辑
        pass
    
    @staticmethod
    @auth_bp.route('/logout', methods=['POST'])
    def logout():
        """登出"""
        session.clear()
        return success(message="登出成功")
    
    @staticmethod
    @auth_bp.route('/user/info')
    def get_user_info():
        """获取用户信息"""
        user_info = session.get('user_info')
        if not user_info:
            return unauthorized("用户未登录")
        return success(user_info)
```

### 4.2 模态数据控制器
```python
# controllers/modal_controller.py
from flask import Blueprint, request
from services.modal_service import ModalService
from utils.result import success, error, bad_request

modal_bp = Blueprint('modal', __name__, url_prefix='/modal')
modal_service = ModalService()

class ModalController:
    """模态数据控制器"""
    
    @staticmethod
    @modal_bp.route('/search')
    def search_page():
        """搜索页面"""
        return render_template('modal/modal_search.html')
    
    @staticmethod
    @modal_bp.route('/api/search')
    def search_modal_data():
        """搜索模态数据API"""
        params = request.args.to_dict()
        
        # 参数验证
        if not params.get('vehicle_model_id'):
            return bad_request("请选择车型")
        
        data = modal_service.search_modal_data(params)
        return success(data, "查询成功")
    
    @staticmethod
    @modal_bp.route('/api/data/<int:data_id>')
    def get_modal_detail(data_id):
        """获取模态数据详情"""
        data = modal_service.get_modal_detail(data_id)
        if not data:
            return error("数据不存在", 404)
        return success(data)
    
    @staticmethod
    @modal_bp.route('/api/vehicles')
    def get_vehicles():
        """获取车型列表"""
        vehicles = modal_service.get_vehicle_list()
        return success(vehicles)
    
    @staticmethod
    @modal_bp.route('/api/components')
    def get_components():
        """获取零部件列表"""
        vehicle_id = request.args.get('vehicle_id')
        components = modal_service.get_component_list(vehicle_id)
        return success(components)
```

## 5. 业务逻辑层设计

### 5.1 模态数据服务
```python
# services/modal_service.py
from models.modal_data_model import ModalDataModel
from models.vehicle_model import VehicleModel
from models.component_model import ComponentModel
from models.test_project_model import TestProjectModel
from models.base_model import db

class ModalService:
    """模态数据业务逻辑"""
    
    def search_modal_data(self, params):
        """搜索模态数据"""
        vehicle_id = params.get('vehicle_model_id')
        component_id = params.get('component_id')
        
        query = db.session.query(
            ModalDataModel,
            TestProjectModel,
            VehicleModel,
            ComponentModel
        ).join(
            TestProjectModel, ModalDataModel.test_project_id == TestProjectModel.id
        ).join(
            VehicleModel, TestProjectModel.vehicle_model_id == VehicleModel.id
        ).outerjoin(
            ComponentModel, TestProjectModel.component_id == ComponentModel.id
        )
        
        # 筛选条件
        if vehicle_id:
            query = query.filter(VehicleModel.id == vehicle_id)
        if component_id:
            query = query.filter(ComponentModel.id == component_id)
        
        results = query.all()
        
        # 数据转换
        data_list = []
        for modal, project, vehicle, component in results:
            item = {
                'id': modal.id,
                'category': component.category if component else '整车',
                'sub_category': component.sub_category if component else '',
                'component_name': component.component_name if component else '整车',
                'frequency': float(modal.frequency),
                'mode_order': modal.mode_order,
                'mode_shape_description': modal.mode_shape_description,
                'direction': modal.direction,
                'damping_ratio': float(modal.damping_ratio) if modal.damping_ratio else None,
                'vehicle_name': vehicle.vehicle_model_name,
                'test_date': project.test_date.strftime('%Y-%m-%d'),
                'test_engineer': project.test_engineer
            }
            data_list.append(item)
        
        return data_list
    
    def get_modal_detail(self, data_id):
        """获取模态数据详情"""
        modal_data = ModalDataModel.query.get(data_id)
        if not modal_data:
            return None
        
        # 关联查询
        project = TestProjectModel.query.get(modal_data.test_project_id)
        vehicle = VehicleModel.query.get(project.vehicle_model_id)
        component = ComponentModel.query.get(project.component_id) if project.component_id else None
        
        detail = modal_data.to_dict()
        detail.update({
            'project_info': project.to_dict(),
            'vehicle_info': vehicle.to_dict(),
            'component_info': component.to_dict() if component else None
        })
        
        return detail
    
    def get_vehicle_list(self):
        """获取车型列表"""
        vehicles = VehicleModel.query.filter_by(status='active').all()
        return [{'id': v.id, 'name': v.vehicle_model_name, 'code': v.vehicle_model_code} for v in vehicles]
    
    def get_component_list(self, vehicle_id=None):
        """获取零部件列表"""
        query = ComponentModel.query
        if vehicle_id:
            # 根据车型筛选零部件（通过测试项目关联）
            query = query.join(TestProjectModel).filter(TestProjectModel.vehicle_model_id == vehicle_id)
        
        components = query.distinct().all()
        return [{'id': c.id, 'name': c.component_name, 'category': c.category} for c in components]
```

## 6. 统一返回结果规范

### 6.1 result.py设计
```python
# utils/result.py
from typing import Any, Optional
from flask import jsonify

class ApiResult:
    """统一API返回结果类"""
    
    def __init__(self, code: int, message: str, data: Any = None):
        self.code = code
        self.message = message
        self.data = data
    
    def to_dict(self):
        """转换为字典"""
        result = {
            'code': self.code,
            'message': self.message
        }
        if self.data is not None:
            result['data'] = self.data
        return result
    
    def to_json(self):
        """转换为JSON响应"""
        return jsonify(self.to_dict())

class ResultBuilder:
    """结果构建器"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> ApiResult:
        """成功结果"""
        return ApiResult(200, message, data)
    
    @staticmethod
    def error(message: str = "操作失败", code: int = 500, data: Any = None) -> ApiResult:
        """错误结果"""
        return ApiResult(code, message, data)
    
    @staticmethod
    def not_found(message: str = "资源不存在") -> ApiResult:
        """资源不存在"""
        return ApiResult(404, message)
    
    @staticmethod
    def bad_request(message: str = "请求参数错误") -> ApiResult:
        """请求参数错误"""
        return ApiResult(400, message)
    
    @staticmethod
    def unauthorized(message: str = "未授权访问") -> ApiResult:
        """未授权"""
        return ApiResult(401, message)
    
    @staticmethod
    def forbidden(message: str = "禁止访问") -> ApiResult:
        """禁止访问"""
        return ApiResult(403, message)

# 便捷方法
def success(data: Any = None, message: str = "操作成功"):
    """返回成功结果"""
    return ResultBuilder.success(data, message).to_json()

def error(message: str = "操作失败", code: int = 500, data: Any = None):
    """返回错误结果"""
    return ResultBuilder.error(message, code, data).to_json()

def not_found(message: str = "资源不存在"):
    """返回资源不存在"""
    return ResultBuilder.not_found(message).to_json()

def bad_request(message: str = "请求参数错误"):
    """返回请求参数错误"""
    return ResultBuilder.bad_request(message).to_json()

def unauthorized(message: str = "未授权访问"):
    """返回未授权"""
    return ResultBuilder.unauthorized(message).to_json()

def forbidden(message: str = "禁止访问"):
    """返回禁止访问"""
    return ResultBuilder.forbidden(message).to_json()
```

## 7. 前端请求封装规范

### 7.1 request.js设计
```javascript
// static/js/request.js

/**
 * 统一请求客户端
 */
class RequestClient {
    constructor() {
        this.baseUrl = '';
        this.timeout = 10000;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }

    /**
     * 发送请求
     * @param {Object} config 请求配置
     */
    async request(config) {
        const {
            url,
            method = 'GET',
            data = null,
            headers = {},
            timeout = this.timeout
        } = config;

        const requestHeaders = { ...this.defaultHeaders, ...headers };
        
        const fetchConfig = {
            method: method.toUpperCase(),
            headers: requestHeaders,
            credentials: 'same-origin'
        };

        if (data && method.toUpperCase() !== 'GET') {
            if (data instanceof FormData) {
                delete fetchConfig.headers['Content-Type'];
                fetchConfig.body = data;
            } else {
                fetchConfig.body = JSON.stringify(data);
            }
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        fetchConfig.signal = controller.signal;
        
        const response = await fetch(this.baseUrl + url, fetchConfig);
        clearTimeout(timeoutId);
        
        const result = await response.json();
        
        if (result.code === 200) {
            return result;
        } else {
            const error = new Error(result.message);
            error.code = result.code;
            error.data = result.data;
            throw error;
        }
    }

    /**
     * GET请求
     */
    get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request({ url: fullUrl, method: 'GET' });
    }

    /**
     * POST请求
     */
    post(url, data = {}) {
        return this.request({ url, method: 'POST', data });
    }

    /**
     * PUT请求
     */
    put(url, data = {}) {
        return this.request({ url, method: 'PUT', data });
    }

    /**
     * DELETE请求
     */
    delete(url) {
        return this.request({ url, method: 'DELETE' });
    }

    /**
     * 文件上传
     */
    upload(url, formData) {
        return this.request({ 
            url, 
            method: 'POST', 
            data: formData,
            headers: {}
        });
    }
}

// 创建全局实例
const request = new RequestClient();

// 消息提示函数
function showMessage(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);
    // TODO: 实现具体的消息提示UI
}

// 全局错误处理
window.addEventListener('unhandledrejection', function(event) {
    const error = event.reason;
    if (error && error.code) {
        showMessage(error.message, 'error');
        if (error.code === 401) {
            window.location.href = '/auth/login';
        }
    }
});
```

### 7.2 页面专用JS示例
```javascript
// static/js/modal_search.js

/**
 * 模态搜索页面管理类
 */
class ModalSearchManager {
    constructor() {
        this.vehicleSelect = document.getElementById('vehicle-select');
        this.componentSelect = document.getElementById('component-select');
        this.searchBtn = document.getElementById('search-btn');
        this.tableBody = document.getElementById('table-body');
        
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.loadVehicles();
        this.bindEvents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        this.vehicleSelect.addEventListener('change', () => {
            this.loadComponents();
        });
        
        this.searchBtn.addEventListener('click', () => {
            this.searchModalData();
        });
    }

    /**
     * 加载车型列表
     */
    async loadVehicles() {
        const result = await request.get('/modal/api/vehicles');
        this.renderVehicleOptions(result.data);
    }

    /**
     * 加载零部件列表
     */
    async loadComponents() {
        const vehicleId = this.vehicleSelect.value;
        if (!vehicleId) return;
        
        const result = await request.get('/modal/api/components', { vehicle_id: vehicleId });
        this.renderComponentOptions(result.data);
    }

    /**
     * 搜索模态数据
     */
    async searchModalData() {
        const params = {
            vehicle_model_id: this.vehicleSelect.value,
            component_id: this.componentSelect.value
        };
        
        if (!params.vehicle_model_id) {
            showMessage('请选择车型', 'warning');
            return;
        }
        
        const result = await request.get('/modal/api/search', params);
        this.renderTable(result.data);
        showMessage('查询成功', 'success');
    }

    /**
     * 渲染车型选项
     */
    renderVehicleOptions(vehicles) {
        this.vehicleSelect.innerHTML = '<option value="">请选择车型</option>';
        vehicles.forEach(vehicle => {
            const option = document.createElement('option');
            option.value = vehicle.id;
            option.textContent = vehicle.name;
            this.vehicleSelect.appendChild(option);
        });
    }

    /**
     * 渲染零部件选项
     */
    renderComponentOptions(components) {
        this.componentSelect.innerHTML = '<option value="">全部零件</option>';
        components.forEach(component => {
            const option = document.createElement('option');
            option.value = component.id;
            option.textContent = component.name;
            this.componentSelect.appendChild(option);
        });
    }

    /**
     * 渲染数据表格
     */
    renderTable(dataList) {
        this.tableBody.innerHTML = '';
        
        dataList.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.category}</td>
                <td>${item.sub_category}</td>
                <td>${item.component_name}</td>
                <td>${item.frequency}</td>
                <td>${item.mode_order}</td>
                <td>${item.mode_shape_description}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="modalSearchManager.showDetail(${item.id})">
                        查看详情
                    </button>
                </td>
            `;
            this.tableBody.appendChild(row);
        });
    }

    /**
     * 显示详情
     */
    async showDetail(dataId) {
        const result = await request.get(`/modal/api/data/${dataId}`);
        // TODO: 显示详情弹窗
        console.log('详情数据:', result.data);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.modalSearchManager = new ModalSearchManager();
});
```

## 8. 错误处理规范

### 8.1 后端错误处理
- 使用统一的返回结果格式
- 不使用大量try-catch，让异常自然抛出
- 在控制器层进行必要的参数验证
- 使用Flask的错误处理机制

### 8.2 前端错误处理
- 使用async/await，让错误自然抛出
- 全局错误处理器统一处理
- 用户友好的错误提示




修改后的设计文档重点改进：

1. **模型文件分离**: 每个表对应一个模型文件，便于维护和扩展
2. **严格命名规范**: 详细规定了Python和前端的命名规则
3. **文件对应关系**: HTML、CSS、JS三者命名保持一致
4. **简化错误处理**: 不使用大量try-catch，让异常自然抛出
5. **规范的代码结构**: 控制器、服务层、模型层职责清晰
6. **统一的请求封装**: 前端请求统一处理，错误统一捕获

这样的设计更加规范和易于维护，请确认是否符合要求。
