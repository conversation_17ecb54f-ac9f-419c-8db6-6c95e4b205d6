# 气密性模块部署说明

## 1. 数据库表创建

首先需要创建气密性相关的数据库表。您已经创建了 `airtightness_tests` 表，还需要创建 `airtightness_images` 表：

```sql
-- 气密性测试图片表
CREATE TABLE airtightness_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_model_id INT NOT NULL COMMENT '车型ID',
    front_compartment_image VARCHAR(255) COMMENT '前舱图片路径',
    door_image VARCHAR(255) COMMENT '车门图片路径',
    tailgate_image VARCHAR(255) COMMENT '尾门图片路径',
    upload_date DATE COMMENT '上传日期',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_model_id) REFERENCES vehicle_models(id)
) COMMENT '气密性测试图片表';
```

## 2. 插入测试数据

运行 `airtightness_test_data.sql` 文件中的SQL语句来插入测试数据：

```bash
mysql -u your_username -p your_database < airtightness_test_data.sql
```

或者在MySQL客户端中直接执行SQL语句。

## 3. 文件结构确认

确认以下文件已正确创建：

### 模型文件
- `models/airtightness_test_model.py`
- `models/airtightness_image_model.py`
- `models/__init__.py` (已更新)

### 服务文件
- `services/airtightness_service.py`

### 控制器文件
- `controllers/airtightness_controller.py`

### 前端文件
- `templates/airtightness/airtightness_comparison.html`
- `templates/airtightness/airtightness_images.html`
- `static/css/airtightness.css`
- `static/js/airtightness.js`

### 主应用文件
- `app.py` (已更新，注册了气密性蓝图)
- `templates/base.html` (已更新，添加了导航菜单)
- `templates/index.html` (已更新，添加了入口卡片)

## 4. 图片目录创建

确认以下目录已创建：
```
static/uploads/airtightness_images/
├── front_compartment/
├── doors/
└── tailgate/
```

## 5. 测试图片准备

为了完整测试功能，您需要准备一些测试图片：

1. 将图片放入对应目录：
   - 前舱图片：`static/uploads/airtightness_images/front_compartment/`
   - 车门图片：`static/uploads/airtightness_images/doors/`
   - 尾门图片：`static/uploads/airtightness_images/tailgate/`

2. 图片命名规范：
   - 宝骏530：`bmw530_front.jpg`, `bmw530_door.jpg`, `bmw530_tailgate.jpg`
   - 宝骏510：`bmw510_front.jpg`, `bmw510_door.jpg`, `bmw510_tailgate.jpg`
   - 五菱宏光MINI EV：`wuling_front.jpg`, `wuling_door.jpg`, `wuling_tailgate.jpg`

## 6. 启动应用

1. 确保所有依赖已安装
2. 启动Flask应用：
   ```bash
   python run.py
   ```

## 7. 功能测试

### 7.1 泄漏量对比功能测试
1. 访问 `http://localhost:5000/airtightness/comparison`
2. 选择一个或多个车型
3. 点击"生成对比表"
4. 查看对比结果表格
5. 测试导出功能

### 7.2 测试图片查看功能测试
1. 访问 `http://localhost:5000/airtightness/images`
2. 选择一个车型
3. 点击"查看图片"
4. 查看三个位置的图片
5. 测试"新窗口打开"功能
6. 测试图片预览功能

## 8. 导航菜单测试

1. 检查左侧导航菜单是否显示"气密性测试"项
2. 展开菜单，确认有"泄漏量对比"和"测试图片"两个子项
3. 点击各菜单项，确认能正确跳转

## 9. 首页入口测试

1. 访问首页 `http://localhost:5000/`
2. 确认有"气密性测试"卡片
3. 点击"进入查询"按钮，确认跳转到对比页面

## 10. 故障排除

### 10.1 导入错误
如果遇到模块导入错误，检查：
- `models/__init__.py` 是否正确导入了新模型
- Python路径是否正确

### 10.2 数据库错误
如果遇到数据库错误，检查：
- 数据库表是否正确创建
- 外键约束是否满足
- 数据库连接配置是否正确

### 10.3 静态文件错误
如果CSS/JS文件无法加载，检查：
- 文件路径是否正确
- 文件权限是否正确

### 10.4 图片显示错误
如果图片无法显示，检查：
- 图片文件是否存在
- 图片路径是否正确
- 图片文件权限是否正确

## 11. 代码测试

运行测试脚本验证代码结构：
```bash
python test_airtightness.py
```

## 12. 生产环境部署注意事项

1. **安全性**：确保上传目录的安全性，限制文件类型和大小
2. **性能**：大量图片时考虑使用CDN或图片压缩
3. **备份**：定期备份数据库和图片文件
4. **监控**：添加日志记录和错误监控

## 13. 扩展功能建议

1. **图片上传功能**：添加管理界面支持图片上传
2. **数据导入功能**：支持批量导入测试数据
3. **图表展示**：添加图表展示对比数据
4. **历史记录**：支持查看历史测试数据
5. **权限控制**：添加不同用户角色的权限控制

完成以上步骤后，气密性模块应该能够正常运行。如有问题，请检查日志文件或联系开发人员。
