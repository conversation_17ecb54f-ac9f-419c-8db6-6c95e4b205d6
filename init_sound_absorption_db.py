#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化垂直入射法吸音系数数据库表
"""

import pymysql
from config import Config

def execute_sql_file():
    """执行SQL文件创建表和插入数据"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE,
            charset='utf8mb4'
        )
        
        print(f"✓ 成功连接到数据库 {Config.MYSQL_DATABASE}")
        
        with connection.cursor() as cursor:
            # 读取SQL文件内容
            with open('sound_absorption_tables.sql', 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句（以分号分割）
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            # 执行每个SQL语句
            for i, statement in enumerate(sql_statements):
                if statement.upper().startswith('USE'):
                    continue  # 跳过USE语句，因为已经连接到指定数据库
                
                try:
                    cursor.execute(statement)
                    print(f"✓ 执行SQL语句 {i+1}/{len(sql_statements)}")
                except Exception as e:
                    if "already exists" in str(e).lower():
                        print(f"⚠ 表已存在，跳过创建: {e}")
                    else:
                        print(f"✗ 执行SQL语句失败: {e}")
                        print(f"语句内容: {statement[:100]}...")
            
            # 提交事务
            connection.commit()
            print("✓ 所有SQL语句执行完成")
            
            # 验证表是否创建成功
            tables_to_check = [
                'sound_insulation_parts',
                'materials', 
                'material_manufacturers',
                'sound_absorption_coefficients'
            ]
            
            for table in tables_to_check:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✓ 表 {table} 创建成功，数据条数: {count}")
        
        connection.close()
        print("\n🎉 数据库初始化完成！")
        
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    execute_sql_file()
