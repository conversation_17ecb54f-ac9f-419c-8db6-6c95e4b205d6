#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
垂直入射法吸音系数功能演示
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def demo_complete_workflow():
    """演示完整的工作流程"""
    print("🎯 垂直入射法吸音系数查询功能演示")
    print("=" * 60)
    
    try:
        # 步骤1：获取零件列表
        print("\n📋 步骤1：获取零件列表")
        response = requests.get(f"{BASE_URL}/sound_absorption/api/parts")
        if response.status_code == 200:
            parts_data = response.json()
            parts = parts_data['data']
            print(f"✓ 成功获取 {len(parts)} 个零件:")
            for part in parts:
                print(f"  • {part['name']}: {part['description']}")
            
            # 选择第一个零件
            selected_part = parts[0]['name']
            print(f"\n🎯 选择零件: {selected_part}")
        else:
            print("✗ 获取零件列表失败")
            return
        
        time.sleep(1)
        
        # 步骤2：根据零件获取材料列表
        print(f"\n🧪 步骤2：获取零件 '{selected_part}' 的材料列表")
        response = requests.get(f"{BASE_URL}/sound_absorption/api/materials?part_name={selected_part}")
        if response.status_code == 200:
            materials_data = response.json()
            materials = materials_data['data']
            print(f"✓ 成功获取 {len(materials)} 种材料:")
            unique_materials = list({m['name']: m for m in materials}.values())
            for material in unique_materials:
                print(f"  • {material['name']}: {material['description']}")
            
            # 选择第一种材料
            selected_material = unique_materials[0]['name']
            print(f"\n🎯 选择材料: {selected_material}")
        else:
            print("✗ 获取材料列表失败")
            return
        
        time.sleep(1)
        
        # 步骤3：根据零件和材料获取克重列表
        print(f"\n⚖️ 步骤3：获取 '{selected_part}' + '{selected_material}' 的克重列表")
        response = requests.get(f"{BASE_URL}/sound_absorption/api/weights?part_name={selected_part}&material_name={selected_material}")
        if response.status_code == 200:
            weights_data = response.json()
            weights = weights_data['data']
            print(f"✓ 成功获取 {len(weights)} 个克重:")
            for weight in weights:
                print(f"  • {weight['display']}")
            
            # 选择第一个克重
            selected_weight = weights[0]['weight']
            print(f"\n🎯 选择克重: {selected_weight}g/m²")
        else:
            print("✗ 获取克重列表失败")
            return
        
        time.sleep(1)
        
        # 步骤4：查询单个克重的吸音系数数据
        print(f"\n📊 步骤4：查询吸音系数数据")
        print(f"查询条件: {selected_part} + {selected_material} + {selected_weight}g/m²")
        response = requests.get(f"{BASE_URL}/sound_absorption/api/absorption_data?part_name={selected_part}&material_name={selected_material}&weight={selected_weight}")
        if response.status_code == 200:
            data = response.json()['data']
            basic_info = data['basic_info']
            table_data = data['table_data']
            chart_data = data['chart_data']
            
            print("✓ 成功获取吸音系数数据")
            print(f"\n📋 基础信息:")
            print(f"  • 零件名称: {basic_info['part_name']}")
            print(f"  • 材料名称: {basic_info['material_name']}")
            print(f"  • 材料厂家: {basic_info['material_manufacturer']}")
            print(f"  • 测试机构: {basic_info['test_institution']}")
            print(f"  • 厚度: {basic_info['thickness']}mm")
            print(f"  • 克重: {basic_info['weight']}g/m²")
            print(f"  • 测试日期: {basic_info['test_date']}")
            print(f"  • 测试工程师: {basic_info['test_engineer']}")
            
            print(f"\n📈 频率数据 (前5个频率点):")
            for i, row in enumerate(table_data[:5]):
                status_text = "✓达标" if row['status'] == 'success' else "✗不达标" if row['status'] == 'danger' else "-"
                print(f"  • {row['frequency']}: 测试值={row['test_value']:.3f}, 目标值={row['target_value']:.3f}, {status_text}")
            print(f"  ... (共{len(table_data)}个频率点)")
            
        else:
            print("✗ 获取吸音系数数据失败")
            return
        
        time.sleep(1)
        
        # 步骤5：多克重对比
        if len(weights) > 1:
            print(f"\n🔄 步骤5：多克重对比分析")
            all_weights = [w['weight'] for w in weights]
            print(f"对比克重: {', '.join([f'{w}g/m²' for w in all_weights])}")
            
            payload = {
                "part_name": selected_part,
                "material_name": selected_material,
                "weights": all_weights
            }
            
            response = requests.post(
                f"{BASE_URL}/sound_absorption/api/multi_weight_comparison",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                comparison_data = response.json()['data']
                print("✓ 成功生成多克重对比数据")
                print(f"  • 对比克重数: {len(all_weights)}")
                print(f"  • 图表系列数: {len(comparison_data['chart_data']['series'])}")
                print(f"  • 频率点数: {len(comparison_data['chart_data']['frequencies'])}")
                
                # 显示1000Hz频率点的对比数据
                table_data = comparison_data['table_data']
                freq_1000_row = next((row for row in table_data if row['frequency'] == '1000Hz'), None)
                if freq_1000_row:
                    print(f"\n📊 1000Hz频率点对比:")
                    for weight in all_weights:
                        test_val = freq_1000_row.get(f'test_{weight}')
                        target_val = freq_1000_row.get(f'target_{weight}')
                        if test_val is not None:
                            print(f"  • {weight}g/m²: 测试值={test_val:.3f}, 目标值={target_val:.3f}")
            else:
                print("✗ 多克重对比失败")
        
        time.sleep(1)
        
        # 步骤6：测试图片信息
        print(f"\n🖼️ 步骤6：获取测试图片信息")
        response = requests.get(f"{BASE_URL}/sound_absorption/api/test_image?part_name={selected_part}&material_name={selected_material}&weight={selected_weight}")
        if response.status_code == 200:
            image_data = response.json()['data']
            print("✓ 成功获取测试图片信息")
            print(f"  • 测试日期: {image_data['test_date']}")
            print(f"  • 测试工程师: {image_data['test_engineer']}")
            print(f"  • 测试地点: {image_data['test_location']}")
            print(f"  • 测试机构: {image_data['test_institution']}")
            print(f"  • 备注: {image_data['remarks']}")
            print(f"  • 图片路径: {image_data['test_image_path'] or '暂无图片'}")
        else:
            print("✗ 获取测试图片信息失败")
        
        print(f"\n🎉 功能演示完成！")
        print("=" * 60)
        print("📝 演示总结:")
        print("✓ 零件、材料、克重三级联动查询")
        print("✓ 单个克重吸音系数数据查询")
        print("✓ 多克重对比分析")
        print("✓ 测试图片信息查看")
        print("✓ 所有API接口正常工作")
        print("\n🌐 访问地址:")
        print(f"  • 主页面: {BASE_URL}/sound_absorption/coefficient_query")
        print(f"  • 应用首页: {BASE_URL}/")
        
    except Exception as e:
        print(f"✗ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    demo_complete_workflow()
