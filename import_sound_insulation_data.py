#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入吸隔声测试数据脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, SoundInsulationAreaModel, SoundInsulationDataModel
from datetime import date

def import_test_data():
    """导入测试数据"""
    with app.app_context():
        try:
            print("开始导入吸隔声测试数据...")
            
            # 1. 插入测试区域数据
            print("插入测试区域数据...")
            areas_data = [
                {'area_name': '前围', 'description': '前围隔声量测试区域'},
                {'area_name': '动力总成', 'description': '动力总成隔声量测试区域'},
                {'area_name': '前挡', 'description': '前挡风玻璃隔声量测试区域'},
                {'area_name': '后挡', 'description': '后挡风玻璃隔声量测试区域'},
                {'area_name': '左前门', 'description': '左前门隔声量测试区域'},
                {'area_name': '右前门', 'description': '右前门隔声量测试区域'},
                {'area_name': '左后门', 'description': '左后门隔声量测试区域'},
                {'area_name': '右后门', 'description': '右后门隔声量测试区域'},
            ]
            
            for area_data in areas_data:
                # 检查是否已存在
                existing = SoundInsulationAreaModel.query.filter_by(area_name=area_data['area_name']).first()
                if not existing:
                    area = SoundInsulationAreaModel(**area_data)
                    db.session.add(area)
                    print(f"  添加区域: {area_data['area_name']}")
                else:
                    print(f"  区域已存在: {area_data['area_name']}")
            
            db.session.commit()
            print("区域数据插入完成")
            
            # 2. 插入隔声量测试数据
            print("插入隔声量测试数据...")
            test_data = [
                # 宝骏530前围隔声量数据
                {
                    'vehicle_model_id': 1, 'area_id': 1,
                    'freq_200': 21.5, 'freq_250': 23.0, 'freq_315': 25.2, 'freq_400': 26.8, 'freq_500': 27.5,
                    'freq_630': 28.9, 'freq_800': 30.2, 'freq_1000': 31.8, 'freq_1250': 33.1, 'freq_1600': 34.5,
                    'freq_2000': 35.8, 'freq_2500': 37.2, 'freq_3150': 38.5, 'freq_4000': 39.8, 'freq_5000': 41.2,
                    'freq_6300': 42.5, 'freq_8000': 43.8, 'freq_10000': 45.1,
                    'test_date': date(2023, 12, 1), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '张工', 'remarks': '前围隔声量测试'
                },
                # 宝骏530动力总成隔声量数据
                {
                    'vehicle_model_id': 1, 'area_id': 2,
                    'freq_200': 18.2, 'freq_250': 19.8, 'freq_315': 21.5, 'freq_400': 23.1, 'freq_500': 24.8,
                    'freq_630': 26.2, 'freq_800': 27.9, 'freq_1000': 29.5, 'freq_1250': 31.2, 'freq_1600': 32.8,
                    'freq_2000': 34.5, 'freq_2500': 36.1, 'freq_3150': 37.8, 'freq_4000': 39.4, 'freq_5000': 41.1,
                    'freq_6300': 42.7, 'freq_8000': 44.4, 'freq_10000': 46.0,
                    'test_date': date(2023, 12, 1), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '张工', 'remarks': '动力总成隔声量测试'
                },
                # 宝骏530前挡隔声量数据
                {
                    'vehicle_model_id': 1, 'area_id': 3,
                    'freq_200': 15.8, 'freq_250': 17.2, 'freq_315': 18.9, 'freq_400': 20.5, 'freq_500': 22.1,
                    'freq_630': 23.8, 'freq_800': 25.4, 'freq_1000': 27.0, 'freq_1250': 28.7, 'freq_1600': 30.3,
                    'freq_2000': 31.9, 'freq_2500': 33.6, 'freq_3150': 35.2, 'freq_4000': 36.8, 'freq_5000': 38.5,
                    'freq_6300': 40.1, 'freq_8000': 41.7, 'freq_10000': 43.4,
                    'test_date': date(2023, 12, 1), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '张工', 'remarks': '前挡隔声量测试'
                },
                # 宝骏510前围隔声量数据
                {
                    'vehicle_model_id': 2, 'area_id': 1,
                    'freq_200': 19.8, 'freq_250': 21.5, 'freq_315': 23.4, 'freq_400': 24.7, 'freq_500': 25.5,
                    'freq_630': 27.1, 'freq_800': 28.8, 'freq_1000': 30.2, 'freq_1250': 31.9, 'freq_1600': 33.2,
                    'freq_2000': 34.8, 'freq_2500': 36.5, 'freq_3150': 37.9, 'freq_4000': 39.2, 'freq_5000': 40.8,
                    'freq_6300': 42.1, 'freq_8000': 43.7, 'freq_10000': 45.0,
                    'test_date': date(2023, 12, 2), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '李工', 'remarks': '前围隔声量测试'
                },
                # 宝骏510动力总成隔声量数据
                {
                    'vehicle_model_id': 2, 'area_id': 2,
                    'freq_200': 16.5, 'freq_250': 18.1, 'freq_315': 19.8, 'freq_400': 21.4, 'freq_500': 23.0,
                    'freq_630': 24.7, 'freq_800': 26.3, 'freq_1000': 27.9, 'freq_1250': 29.6, 'freq_1600': 31.2,
                    'freq_2000': 32.8, 'freq_2500': 34.5, 'freq_3150': 36.1, 'freq_4000': 37.7, 'freq_5000': 39.4,
                    'freq_6300': 41.0, 'freq_8000': 42.6, 'freq_10000': 44.3,
                    'test_date': date(2023, 12, 2), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '李工', 'remarks': '动力总成隔声量测试'
                },
                # 宝骏510前挡隔声量数据
                {
                    'vehicle_model_id': 2, 'area_id': 3,
                    'freq_200': 14.2, 'freq_250': 15.8, 'freq_315': 17.5, 'freq_400': 19.1, 'freq_500': 20.7,
                    'freq_630': 22.4, 'freq_800': 24.0, 'freq_1000': 25.6, 'freq_1250': 27.3, 'freq_1600': 28.9,
                    'freq_2000': 30.5, 'freq_2500': 32.2, 'freq_3150': 33.8, 'freq_4000': 35.4, 'freq_5000': 37.1,
                    'freq_6300': 38.7, 'freq_8000': 40.3, 'freq_10000': 42.0,
                    'test_date': date(2023, 12, 2), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '李工', 'remarks': '前挡隔声量测试'
                },
                # 五菱宏光MINI EV前围隔声量数据
                {
                    'vehicle_model_id': 3, 'area_id': 1,
                    'freq_200': 23.1, 'freq_250': 24.6, 'freq_315': 26.3, 'freq_400': 27.9, 'freq_500': 28.8,
                    'freq_630': 30.4, 'freq_800': 32.1, 'freq_1000': 33.5, 'freq_1250': 35.2, 'freq_1600': 36.8,
                    'freq_2000': 38.5, 'freq_2500': 40.1, 'freq_3150': 41.8, 'freq_4000': 43.4, 'freq_5000': 45.1,
                    'freq_6300': 46.7, 'freq_8000': 48.4, 'freq_10000': 50.0,
                    'test_date': date(2023, 12, 3), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '王工', 'remarks': '前围隔声量测试'
                },
                # 五菱宏光MINI EV动力总成隔声量数据（电动车，隔声量相对较高）
                {
                    'vehicle_model_id': 3, 'area_id': 2,
                    'freq_200': 25.8, 'freq_250': 27.2, 'freq_315': 28.9, 'freq_400': 30.5, 'freq_500': 32.1,
                    'freq_630': 33.8, 'freq_800': 35.4, 'freq_1000': 37.0, 'freq_1250': 38.7, 'freq_1600': 40.3,
                    'freq_2000': 41.9, 'freq_2500': 43.6, 'freq_3150': 45.2, 'freq_4000': 46.8, 'freq_5000': 48.5,
                    'freq_6300': 50.1, 'freq_8000': 51.7, 'freq_10000': 53.4,
                    'test_date': date(2023, 12, 3), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '王工', 'remarks': '电动动力总成隔声量测试'
                },
                # 五菱宏光MINI EV前挡隔声量数据
                {
                    'vehicle_model_id': 3, 'area_id': 3,
                    'freq_200': 17.5, 'freq_250': 19.1, 'freq_315': 20.8, 'freq_400': 22.4, 'freq_500': 24.0,
                    'freq_630': 25.7, 'freq_800': 27.3, 'freq_1000': 28.9, 'freq_1250': 30.6, 'freq_1600': 32.2,
                    'freq_2000': 33.8, 'freq_2500': 35.5, 'freq_3150': 37.1, 'freq_4000': 38.7, 'freq_5000': 40.4,
                    'freq_6300': 42.0, 'freq_8000': 43.6, 'freq_10000': 45.3,
                    'test_date': date(2023, 12, 3), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '王工', 'remarks': '前挡隔声量测试'
                },
                # 宝骏530左前门隔声量数据
                {
                    'vehicle_model_id': 1, 'area_id': 5,
                    'freq_200': 12.8, 'freq_250': 14.2, 'freq_315': 15.9, 'freq_400': 17.5, 'freq_500': 19.1,
                    'freq_630': 20.8, 'freq_800': 22.4, 'freq_1000': 24.0, 'freq_1250': 25.7, 'freq_1600': 27.3,
                    'freq_2000': 28.9, 'freq_2500': 30.6, 'freq_3150': 32.2, 'freq_4000': 33.8, 'freq_5000': 35.5,
                    'freq_6300': 37.1, 'freq_8000': 38.7, 'freq_10000': 40.4,
                    'test_date': date(2023, 12, 4), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '张工', 'remarks': '左前门隔声量测试'
                },
                # 宝骏510左前门隔声量数据
                {
                    'vehicle_model_id': 2, 'area_id': 5,
                    'freq_200': 11.5, 'freq_250': 13.1, 'freq_315': 14.8, 'freq_400': 16.4, 'freq_500': 18.0,
                    'freq_630': 19.7, 'freq_800': 21.3, 'freq_1000': 22.9, 'freq_1250': 24.6, 'freq_1600': 26.2,
                    'freq_2000': 27.8, 'freq_2500': 29.5, 'freq_3150': 31.1, 'freq_4000': 32.7, 'freq_5000': 34.4,
                    'freq_6300': 36.0, 'freq_8000': 37.6, 'freq_10000': 39.3,
                    'test_date': date(2023, 12, 5), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '李工', 'remarks': '左前门隔声量测试'
                },
                # 五菱宏光MINI EV左前门隔声量数据
                {
                    'vehicle_model_id': 3, 'area_id': 5,
                    'freq_200': 13.9, 'freq_250': 15.5, 'freq_315': 17.2, 'freq_400': 18.8, 'freq_500': 20.4,
                    'freq_630': 22.1, 'freq_800': 23.7, 'freq_1000': 25.3, 'freq_1250': 27.0, 'freq_1600': 28.6,
                    'freq_2000': 30.2, 'freq_2500': 31.9, 'freq_3150': 33.5, 'freq_4000': 35.1, 'freq_5000': 36.8,
                    'freq_6300': 38.4, 'freq_8000': 40.0, 'freq_10000': 41.7,
                    'test_date': date(2023, 12, 6), 'test_location': '上汽通用五菱NVH实验室',
                    'test_engineer': '王工', 'remarks': '左前门隔声量测试'
                }
            ]
            
            for data in test_data:
                # 检查是否已存在
                existing = SoundInsulationDataModel.query.filter_by(
                    vehicle_model_id=data['vehicle_model_id'],
                    area_id=data['area_id']
                ).first()
                
                if not existing:
                    sound_data = SoundInsulationDataModel(**data)
                    db.session.add(sound_data)
                    print(f"  添加数据: 车型{data['vehicle_model_id']} 区域{data['area_id']}")
                else:
                    print(f"  数据已存在: 车型{data['vehicle_model_id']} 区域{data['area_id']}")
            
            db.session.commit()
            print("隔声量测试数据插入完成")
            print("所有数据导入成功！")
            
        except Exception as e:
            print(f"导入数据时发生错误: {e}")
            db.session.rollback()
            return False
        
        return True

if __name__ == '__main__':
    success = import_test_data()
    if success:
        print("数据导入完成！")
    else:
        print("数据导入失败！")
        sys.exit(1)
