# 模态对比图字体和样式配置说明

## 配置文件位置
文件路径：`/static/js/modal_search.js`
函数：`renderComparisonChart()` 方法中的 Chart.js 配置

## 字体大小配置项

### 1. 图表标题 (Title)
```javascript
plugins: {
    title: {
        font: {
            size: 20,        // 当前值：20，建议范围：16-24
            weight: 'bold',  // 字体粗细：'normal', '500', '600', 'bold'
            family: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif"
        }
    }
}
```

### 2. 图例 (Legend)
```javascript
plugins: {
    legend: {
        labels: {
            font: {
                size: 14,        // 当前值：14，建议范围：12-16
                weight: '600'    // 字体粗细：'normal', '500', '600', 'bold'
            }
        }
    }
}
```

### 3. X轴标题
```javascript
scales: {
    x: {
        title: {
            font: {
                size: 16,        // 当前值：16，建议范围：14-18
                weight: 'bold'   // 字体粗细：'normal', '500', '600', 'bold'
            }
        }
    }
}
```

### 4. X轴刻度标签
```javascript
scales: {
    x: {
        ticks: {
            font: {
                size: 13,        // 当前值：13，建议范围：11-15
                weight: '500'    // 字体粗细：'normal', '500', '600', 'bold'
            }
        }
    }
}
```

### 5. Y轴标题
```javascript
scales: {
    y: {
        title: {
            font: {
                size: 16,        // 当前值：16，建议范围：14-18
                weight: 'bold'   // 字体粗细：'normal', '500', '600', 'bold'
            }
        }
    }
}
```

### 6. Y轴刻度标签
```javascript
scales: {
    y: {
        ticks: {
            font: {
                size: 13,        // 当前值：13，建议范围：11-15
                weight: '500'    // 字体粗细：'normal', '500', '600', 'bold'
            }
        }
    }
}
```

## 散点样式配置项

### 散点大小和样式
在 `prepareChartDatasets()` 方法中：
```javascript
return {
    pointRadius: 8,           // 散点大小，当前值：8，建议范围：6-12
    pointHoverRadius: 12,     // 鼠标悬停时散点大小，当前值：12，建议范围：8-16
    borderWidth: 2            // 散点边框宽度，当前值：2，建议范围：1-3
};
```

## 字体族说明
当前使用的字体族（按优先级排序）：
- `-apple-system`：苹果系统字体（macOS/iOS）
- `BlinkMacSystemFont`：Chrome在macOS上的系统字体
- `'Segoe UI'`：Windows 10/11系统字体
- `'Microsoft YaHei'`：微软雅黑（Windows中文）
- `'PingFang SC'`：苹果苹方字体（macOS中文）
- `'Hiragino Sans GB'`：冬青黑体（macOS中文）
- `sans-serif`：系统默认无衬线字体

这个字体族配置确保在不同操作系统上都能显示最清晰的字体。

## 字体粗细选项说明
- `'normal'` 或 `400`：正常粗细
- `'500'`：稍微加粗
- `'600'`：中等加粗
- `'bold'` 或 `700`：粗体
- `'800'`：很粗
- `'900'`：最粗

## 快速调整建议

### 如果觉得字体太小：
- 标题：20 → 22
- 图例：14 → 15
- 轴标题：16 → 17
- 轴刻度：13 → 14

### 如果觉得字体太大：
- 标题：20 → 18
- 图例：14 → 13
- 轴标题：16 → 15
- 轴刻度：13 → 12

### 如果觉得散点太小：
- pointRadius：8 → 10
- pointHoverRadius：12 → 14

### 如果觉得散点太大：
- pointRadius：8 → 6
- pointHoverRadius：12 → 10

## 如果需要更换其他字体
可以将 `family` 属性修改为：

### 常用清晰字体选项：
1. **系统字体（推荐）**：
   ```javascript
   family: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif"
   ```

2. **Google字体（需要引入）**：
   ```javascript
   family: "'Roboto', 'Noto Sans SC', sans-serif"
   ```

3. **经典Web字体**：
   ```javascript
   family: "'Arial', 'Helvetica Neue', 'Microsoft YaHei', sans-serif"
   ```

4. **等宽字体（适合数字）**：
   ```javascript
   family: "'Consolas', 'Monaco', 'Courier New', monospace"
   ```

## 修改步骤
1. 打开 `/static/js/modal_search.js` 文件
2. 找到 `renderComparisonChart()` 方法
3. 修改对应的数值和字体族
4. 保存文件
5. 刷新页面查看效果

## CSS字体平滑优化
在 `/static/css/modal_search.css` 中已添加：
```css
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
```
这些属性可以让字体在不同浏览器中显示更加平滑清晰。
