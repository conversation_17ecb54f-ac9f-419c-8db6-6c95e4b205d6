-- 吸隔声模块测试数据
USE nvh_data;

-- 插入测试区域数据
INSERT INTO sound_insulation_areas (area_name, description) VALUES
('前围', '前围隔声量测试区域'),
('动力总成', '动力总成隔声量测试区域'),
('前挡', '前挡风玻璃隔声量测试区域'),
('后挡', '后挡风玻璃隔声量测试区域'),
('左前门', '左前门隔声量测试区域'),
('右前门', '右前门隔声量测试区域'),
('左后门', '左后门隔声量测试区域'),
('右后门', '右后门隔声量测试区域');

-- 插入宝骏530前围隔声量数据
INSERT INTO sound_insulation_data (
    vehicle_model_id, area_id, 
    freq_200, freq_250, freq_315, freq_400, freq_500, freq_630, freq_800, freq_1000, freq_1250, 
    freq_1600, freq_2000, freq_2500, freq_3150, freq_4000, freq_5000, freq_6300, freq_8000, freq_10000,
    test_date, test_location, test_engineer, remarks
) VALUES
(1, 1, 21.5, 23.0, 25.2, 26.8, 27.5, 28.9, 30.2, 31.8, 33.1, 34.5, 35.8, 37.2, 38.5, 39.8, 41.2, 42.5, 43.8, 45.1, '2023-12-01', '上汽通用五菱NVH实验室', '张工', '前围隔声量测试'),

-- 插入宝骏530动力总成隔声量数据
(1, 2, 18.2, 19.8, 21.5, 23.1, 24.8, 26.2, 27.9, 29.5, 31.2, 32.8, 34.5, 36.1, 37.8, 39.4, 41.1, 42.7, 44.4, 46.0, '2023-12-01', '上汽通用五菱NVH实验室', '张工', '动力总成隔声量测试'),

-- 插入宝骏530前挡隔声量数据
(1, 3, 15.8, 17.2, 18.9, 20.5, 22.1, 23.8, 25.4, 27.0, 28.7, 30.3, 31.9, 33.6, 35.2, 36.8, 38.5, 40.1, 41.7, 43.4, '2023-12-01', '上汽通用五菱NVH实验室', '张工', '前挡隔声量测试'),

-- 插入宝骏510前围隔声量数据
(2, 1, 19.8, 21.5, 23.4, 24.7, 25.5, 27.1, 28.8, 30.2, 31.9, 33.2, 34.8, 36.5, 37.9, 39.2, 40.8, 42.1, 43.7, 45.0, '2023-12-02', '上汽通用五菱NVH实验室', '李工', '前围隔声量测试'),

-- 插入宝骏510动力总成隔声量数据
(2, 2, 16.5, 18.1, 19.8, 21.4, 23.0, 24.7, 26.3, 27.9, 29.6, 31.2, 32.8, 34.5, 36.1, 37.7, 39.4, 41.0, 42.6, 44.3, '2023-12-02', '上汽通用五菱NVH实验室', '李工', '动力总成隔声量测试'),

-- 插入宝骏510前挡隔声量数据
(2, 3, 14.2, 15.8, 17.5, 19.1, 20.7, 22.4, 24.0, 25.6, 27.3, 28.9, 30.5, 32.2, 33.8, 35.4, 37.1, 38.7, 40.3, 42.0, '2023-12-02', '上汽通用五菱NVH实验室', '李工', '前挡隔声量测试'),

-- 插入五菱宏光MINI EV前围隔声量数据
(3, 1, 23.1, 24.6, 26.3, 27.9, 28.8, 30.4, 32.1, 33.5, 35.2, 36.8, 38.5, 40.1, 41.8, 43.4, 45.1, 46.7, 48.4, 50.0, '2023-12-03', '上汽通用五菱NVH实验室', '王工', '前围隔声量测试'),

-- 插入五菱宏光MINI EV动力总成隔声量数据（电动车，隔声量相对较高）
(3, 2, 25.8, 27.2, 28.9, 30.5, 32.1, 33.8, 35.4, 37.0, 38.7, 40.3, 41.9, 43.6, 45.2, 46.8, 48.5, 50.1, 51.7, 53.4, '2023-12-03', '上汽通用五菱NVH实验室', '王工', '电动动力总成隔声量测试'),

-- 插入五菱宏光MINI EV前挡隔声量数据
(3, 3, 17.5, 19.1, 20.8, 22.4, 24.0, 25.7, 27.3, 28.9, 30.6, 32.2, 33.8, 35.5, 37.1, 38.7, 40.4, 42.0, 43.6, 45.3, '2023-12-03', '上汽通用五菱NVH实验室', '王工', '前挡隔声量测试'),

-- 插入宝骏530左前门隔声量数据
(1, 5, 12.8, 14.2, 15.9, 17.5, 19.1, 20.8, 22.4, 24.0, 25.7, 27.3, 28.9, 30.6, 32.2, 33.8, 35.5, 37.1, 38.7, 40.4, '2023-12-04', '上汽通用五菱NVH实验室', '张工', '左前门隔声量测试'),

-- 插入宝骏510左前门隔声量数据
(2, 5, 11.5, 13.1, 14.8, 16.4, 18.0, 19.7, 21.3, 22.9, 24.6, 26.2, 27.8, 29.5, 31.1, 32.7, 34.4, 36.0, 37.6, 39.3, '2023-12-05', '上汽通用五菱NVH实验室', '李工', '左前门隔声量测试'),

-- 插入五菱宏光MINI EV左前门隔声量数据
(3, 5, 13.9, 15.5, 17.2, 18.8, 20.4, 22.1, 23.7, 25.3, 27.0, 28.6, 30.2, 31.9, 33.5, 35.1, 36.8, 38.4, 40.0, 41.7, '2023-12-06', '上汽通用五菱NVH实验室', '王工', '左前门隔声量测试');
